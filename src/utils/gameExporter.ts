/**
 * Professional Game Exporter Utility
 *
 * Exports generated slot machine games as standalone HTML files
 * that perfectly replicate the PixiSlotMockup preview experience.
 *
 * Features:
 * - Professional PixiJS-based slot machine
 * - Realistic spin animations with GSAP
 * - Win highlighting and celebrations
 * - Loading screens and splash screens
 * - Logo positioning and frame adjustments
 * - Perfect symbol positioning and scaling
 * - All animations from the preview
 */

import { GameConfig } from '../types';

interface ExportOptions {
  gameId: string;
  gameConfig: Partial<GameConfig>;
  title?: string;
  includeAnalytics?: boolean;
  customCSS?: string;
  customJS?: string;
}

interface ExportResult {
  success: boolean;
  htmlContent?: string;
  assets?: { [key: string]: string };
  error?: string;
}

/**
 * Export a game as a standalone HTML file
 */
export const exportGameAsHTML = async (options: ExportOptions): Promise<ExportResult> => {
  try {
    const {
      gameId,
      gameConfig,
      title = 'Slot Machine Game',
      includeAnalytics = false,
      customCSS = '',
      customJS = ''
    } = options;

    console.log('🎮 [GameExporter] Exporting game:', gameId);

    // Helper function to get symbol URLs from both array and object formats
    const getSymbolUrls = (symbols: string[] | Record<string, string> | undefined): string[] => {
      if (!symbols) return [];
      if (Array.isArray(symbols)) return symbols.filter(Boolean);
      return Object.values(symbols).filter(Boolean);
    };

    // Extract game assets with enhanced support
    const symbolsRaw = gameConfig?.theme?.generated?.symbols;
    const symbolsArray = getSymbolUrls(symbolsRaw);
    const background = gameConfig?.theme?.generated?.background;
    const frame = gameConfig?.theme?.generated?.frame;
    const logo = gameConfig?.theme?.generated?.logo;
    const loadingScreen = gameConfig?.theme?.generated?.loadingScreen;
    const splashScreen = gameConfig?.theme?.generated?.splashScreen;
    const bonusSymbols = gameConfig?.theme?.generated?.bonusSymbols || {};

    // Extract positioning and adjustment data
    const gridAdjustments = gameConfig?.theme?.generated?.gridAdjustments || { position: { x: 0, y: 0 }, scale: 100, stretch: { x: 100, y: 100 } };
    const frameAdjustments = gameConfig?.theme?.generated?.frameAdjustments || { position: { x: 0, y: 0 }, scale: 100, stretch: { x: 100, y: 100 } };
    const backgroundAdjustments = gameConfig?.theme?.generated?.backgroundAdjustments || { position: { x: 0, y: 0 }, scale: 100, fit: 'cover' };
    const logoPosition = gameConfig?.theme?.generated?.logoPosition || { x: 0, y: -50 };
    const logoScale = gameConfig?.theme?.generated?.logoScale || 100;

    // Generate the professional standalone HTML
    const htmlContent = generateStandaloneHTML({
      gameId,
      title,
      gameConfig,
      symbols: symbolsArray,
      background,
      frame,
      logo,
      loadingScreen,
      splashScreen,
      bonusSymbols,
      gridAdjustments,
      frameAdjustments,
      backgroundAdjustments,
      logoPosition,
      logoScale,
      customCSS,
      customJS,
      includeAnalytics
    });

    // Collect all assets that need to be included
    const assets: { [key: string]: string } = {};
    
    // Add symbols
    symbolsArray.forEach((symbol, index) => {
      if (symbol && typeof symbol === 'string') {
        assets[`symbol_${index}.png`] = symbol;
      }
    });

    // Add background
    if (background) {
      assets['background.png'] = background;
    }

    // Add frame
    if (frame) {
      assets['frame.png'] = frame;
    }

    // Add logo
    if (logo) {
      assets['logo.png'] = logo;
    }

    // Add loading screen
    if (loadingScreen) {
      assets['loading-screen.png'] = loadingScreen;
    }

    // Add splash screen
    if (splashScreen) {
      assets['splash-screen.png'] = splashScreen;
    }

    // Add bonus symbols
    Object.entries(bonusSymbols).forEach(([key, url]) => {
      if (url) {
        assets[`${key}.png`] = url;
      }
    });

    console.log('✅ [GameExporter] Game exported successfully');

    return {
      success: true,
      htmlContent,
      assets
    };

  } catch (error) {
    console.error('❌ [GameExporter] Export failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown export error'
    };
  }
};

/**
 * Generate professional standalone HTML content that replicates PixiSlotMockup
 */
const generateStandaloneHTML = (params: {
  gameId: string;
  title: string;
  gameConfig: Partial<GameConfig>;
  symbols: string[];
  background?: string;
  frame?: string;
  logo?: string;
  loadingScreen?: string;
  splashScreen?: string;
  bonusSymbols: Record<string, string>;
  gridAdjustments: any;
  frameAdjustments: any;
  backgroundAdjustments: any;
  logoPosition: { x: number; y: number };
  logoScale: number;
  customCSS: string;
  customJS: string;
  includeAnalytics: boolean;
}): string => {
  const {
    gameId,
    title,
    gameConfig,
    symbols,
    background,
    frame,
    logo,
    loadingScreen,
    splashScreen,
    bonusSymbols,
    gridAdjustments,
    frameAdjustments,
    backgroundAdjustments,
    logoPosition,
    logoScale,
    customCSS,
    customJS,
    includeAnalytics
  } = params;

  const reels = gameConfig?.reels?.layout?.reels || 5;
  const rows = gameConfig?.reels?.layout?.rows || 3;
  const minBet = gameConfig?.bet?.min || 0.2;
  const maxBet = gameConfig?.bet?.max || 100;
  const initialBalance = gameConfig?.playerExperience?.initialBalance || 1000;
  const gameTitle = gameConfig?.displayName || title;

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${gameTitle}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- PIXI.js v7.4.3 -->
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@7.4.3/dist/pixi.min.js"></script>

    <!-- GSAP for professional animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>

    <!-- PIXI Filters for glow effects -->
    <script src="https://cdn.jsdelivr.net/npm/@pixi/filter-glow@5.1.1/dist/filter-glow.min.js"></script>
    
    <style>
        /* Professional Slot Machine Styling - Matches PixiSlotMockup */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-logo {
            width: 200px;
            height: 200px;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        .loading-text {
            color: #fff;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .loading-bar {
            width: 300px;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #e53e3e, #f56565);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        /* Game Container */
        .game-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        }

        .slot-machine {
            position: relative;
            width: 1200px;
            height: 800px;
            max-width: 95vw;
            max-height: 95vh;
            border-radius: 20px;
            box-shadow:
                0 0 50px rgba(0, 0, 0, 0.5),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(0, 0, 0, 0.2));
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .game-canvas {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            display: block;
        }

        /* Logo Positioning */
        .game-logo {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10;
            max-width: 200px;
            height: auto;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        /* UI Overlay - Professional Design */
        .ui-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.9) 100%);
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(15px);
            border-radius: 0 0 20px 20px;
        }

        .game-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-label {
            opacity: 0.8;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .info-value {
            font-size: 20px;
            font-weight: bold;
            color: #4ade80;
        }

        .win-value {
            color: #fbbf24;
            text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
        }

        /* Game Controls */
        .game-controls {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .bet-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .bet-label {
            color: white;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.8;
        }

        .bet-amount {
            color: #4ade80;
            font-size: 18px;
            font-weight: bold;
            min-width: 80px;
            text-align: center;
        }

        .bet-btn {
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(145deg, #4a5568, #2d3748);
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .bet-btn:hover {
            background: linear-gradient(145deg, #2d3748, #1a202c);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }

        .bet-btn:active {
            transform: translateY(0);
        }

        /* Main Action Buttons */
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-spin {
            background: linear-gradient(145deg, #e53e3e, #c53030);
            color: white;
            min-width: 140px;
            font-size: 18px;
            position: relative;
        }

        .btn-spin:hover {
            background: linear-gradient(145deg, #c53030, #9c2626);
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(229, 62, 62, 0.4);
        }

        .btn-spin:disabled {
            background: linear-gradient(145deg, #4a5568, #2d3748);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .btn-spin.spinning {
            animation: spinPulse 0.5s infinite alternate;
        }

        @keyframes spinPulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.05); }
        }

        .btn-auto {
            background: linear-gradient(145deg, #3182ce, #2c5282);
            color: white;
            min-width: 120px;
        }

        .btn-auto:hover {
            background: linear-gradient(145deg, #2c5282, #2a4365);
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(49, 130, 206, 0.4);
        }

        .btn-auto.active {
            background: linear-gradient(145deg, #38a169, #2f855a);
            box-shadow: 0 0 20px rgba(56, 161, 105, 0.5);
        }

        /* Win Celebration Effects */
        .win-celebration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .win-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            font-weight: bold;
            color: #fbbf24;
            text-shadow: 0 0 20px rgba(251, 191, 36, 0.8);
            opacity: 0;
            animation: winTextAnimation 3s ease-out;
        }

        @keyframes winTextAnimation {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
            20% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
            80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .slot-machine {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
            }

            .ui-overlay {
                padding: 20px;
                flex-direction: column;
                gap: 20px;
            }

            .game-controls {
                width: 100%;
                justify-content: center;
            }

            .bet-controls {
                flex: 1;
                justify-content: center;
            }
        }

        ${customCSS}
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        ${loadingScreen ? `<img src="${loadingScreen}" alt="Loading" class="loading-logo">` : ''}
        <div class="loading-text">Loading ${gameTitle}...</div>
        <div class="loading-bar">
            <div class="loading-progress" id="loadingProgress"></div>
        </div>
    </div>

    <!-- Game Container -->
    <div class="game-container">
        <div class="slot-machine">
            <!-- Logo -->
            ${logo ? `<img src="${logo}" alt="Game Logo" class="game-logo" style="transform: translateX(-50%) translateY(${logoPosition.y}px) translateX(${logoPosition.x}px) scale(${logoScale / 100});">` : ''}

            <!-- Game Canvas -->
            <canvas id="gameCanvas" class="game-canvas"></canvas>

            <!-- Win Celebration -->
            <div class="win-celebration" id="winCelebration">
                <div class="win-text" id="winText"></div>
            </div>

            <!-- UI Overlay -->
            <div class="ui-overlay">
                <div class="game-info">
                    <div class="info-item">
                        <span class="info-label">Balance</span>
                        <span class="info-value">$<span id="balance">${initialBalance.toFixed(2)}</span></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Win</span>
                        <span class="info-value win-value">$<span id="win">0.00</span></span>
                    </div>
                </div>

                <div class="game-controls">
                    <div class="bet-controls">
                        <span class="bet-label">Bet</span>
                        <button class="bet-btn" id="betDown">-</button>
                        <span class="bet-amount">$<span id="betAmount">${minBet.toFixed(2)}</span></span>
                        <button class="bet-btn" id="betUp">+</button>
                    </div>

                    <button class="btn btn-auto" id="autoBtn">Auto</button>
                    <button class="btn btn-spin" id="spinBtn">Spin</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Professional Game Configuration (embedded from your GameStore)
        const GAME_CONFIG = ${JSON.stringify({
          gameId,
          title: gameTitle,
          reels,
          rows,
          symbols: symbols,
          background,
          frame,
          logo,
          loadingScreen,
          splashScreen,
          bonusSymbols,
          minBet,
          maxBet,
          initialBalance,
          rtp: gameConfig?.rtp?.target || 96,
          volatility: gameConfig?.volatility?.level || 'medium',
          bonus: gameConfig?.bonus || {},
          // Positioning and adjustments
          gridAdjustments: gridAdjustments,
          frameAdjustments: frameAdjustments,
          backgroundAdjustments: backgroundAdjustments,
          logoPosition: logoPosition,
          logoScale: logoScale
        }, null, 2)};

        // Professional Game State
        let gameState = {
            balance: ${initialBalance},
            bet: ${minBet},
            win: 0,
            isSpinning: false,
            isAutoplayActive: false,
            autoplayCount: 0,
            spinHistory: [],
            // Win tracking
            totalWins: 0,
            bigWins: 0,
            megaWins: 0,
            bonusTriggered: 0,
            // Animation state
            currentWinType: 'none',
            winPositions: [],
            isLoading: true,
            loadingProgress: 0
        };

        // Professional Loading System
        let loadingAssets = [];
        let loadedAssets = 0;

        // Add all assets to loading queue
        if (GAME_CONFIG.background) loadingAssets.push(GAME_CONFIG.background);
        if (GAME_CONFIG.frame) loadingAssets.push(GAME_CONFIG.frame);
        if (GAME_CONFIG.logo) loadingAssets.push(GAME_CONFIG.logo);
        if (GAME_CONFIG.loadingScreen) loadingAssets.push(GAME_CONFIG.loadingScreen);
        if (GAME_CONFIG.splashScreen) loadingAssets.push(GAME_CONFIG.splashScreen);
        GAME_CONFIG.symbols.forEach(symbol => {
            if (symbol) loadingAssets.push(symbol);
        });
        Object.values(GAME_CONFIG.bonusSymbols).forEach(symbol => {
            if (symbol) loadingAssets.push(symbol);
        });

        console.log('🎮 Loading', loadingAssets.length, 'assets for', GAME_CONFIG.title);

        // DOM Elements
        const canvas = document.getElementById('gameCanvas');
        const balanceEl = document.getElementById('balance');
        const winEl = document.getElementById('win');
        const betAmountEl = document.getElementById('betAmount');
        const spinBtn = document.getElementById('spinBtn');
        const autoBtn = document.getElementById('autoBtn');
        const betUpBtn = document.getElementById('betUp');
        const betDownBtn = document.getElementById('betDown');
        const loadingScreen = document.getElementById('loadingScreen');
        const loadingProgress = document.getElementById('loadingProgress');
        const winCelebration = document.getElementById('winCelebration');
        const winText = document.getElementById('winText');

        // PIXI Application and Game Objects
        let app = null;
        let reelContainers = [];
        let symbolSprites = [];
        let backgroundSprite = null;
        let frameSprite = null;
        let logoSprite = null;
        let currentSpinResult = [];
        let winHighlightFilters = [];

        // Professional Asset Loading with Progress
        const updateLoadingProgress = (progress) => {
            gameState.loadingProgress = progress;
            if (loadingProgress) {
                loadingProgress.style.width = progress + '%';
            }
            console.log('🔄 Loading progress:', progress + '%');
        };

        const hideLoadingScreen = () => {
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    gameState.isLoading = false;
                }, 500);
            }
        };

        // Professional Game Initialization
        async function initGame() {
            console.log('🎮 Initializing professional slot machine:', GAME_CONFIG.title);

            try {
                // Update loading progress
                updateLoadingProgress(10);

                // Create PIXI application with professional settings
                app = new PIXI.Application({
                    view: canvas,
                    width: canvas.offsetWidth,
                    height: canvas.offsetHeight,
                    backgroundColor: 0x000000,
                    antialias: true,
                    resolution: window.devicePixelRatio || 1,
                    autoDensity: true,
                    powerPreference: 'high-performance'
                });

                updateLoadingProgress(20);

                // Load all game assets with progress tracking
                await loadGameAssets();
                updateLoadingProgress(70);

                // Render the professional slot machine
                await renderSlotMachine();
                updateLoadingProgress(90);

                // Setup event listeners and game logic
                setupEventListeners();
                setupGameLogic();
                updateLoadingProgress(100);

                // Hide loading screen and show game
                setTimeout(() => {
                    hideLoadingScreen();
                    console.log('✅ Professional slot machine initialized successfully');
                }, 500);

            } catch (error) {
                console.error('❌ Failed to initialize game:', error);
                alert('Failed to load the game. Please refresh and try again.');
            }
        }
        
        // Professional Asset Loading System
        async function loadGameAssets() {
            console.log('📦 Loading professional game assets...');

            const totalAssets = loadingAssets.length;
            let loadedCount = 0;

            // Load assets with progress tracking
            for (const assetUrl of loadingAssets) {
                try {
                    await PIXI.Assets.load(assetUrl);
                    loadedCount++;
                    const progress = 20 + (loadedCount / totalAssets) * 50; // 20-70% range
                    updateLoadingProgress(progress);
                    console.log(\`✅ Loaded asset \${loadedCount}/\${totalAssets}: \${assetUrl.substring(assetUrl.lastIndexOf('/') + 1)}\`);
                } catch (error) {
                    console.warn(\`⚠️ Failed to load asset: \${assetUrl}\`, error);
                    loadedCount++; // Still count as processed
                    const progress = 20 + (loadedCount / totalAssets) * 50;
                    updateLoadingProgress(progress);
                }
            }

            // Pre-create glow filters for win highlighting
            for (let i = 0; i < GAME_CONFIG.reels * GAME_CONFIG.rows; i++) {
                const glowFilter = new PIXI.filters.GlowFilter({
                    distance: 15,
                    outerStrength: 2,
                    innerStrength: 1,
                    color: 0x00ff00,
                    quality: 0.5
                });
                winHighlightFilters.push(glowFilter);
            }

            console.log('✅ All assets loaded successfully');
        }
        
        // Render the slot machine
        function renderSlotMachine() {
            // Clear stage
            app.stage.removeChildren();
            
            // Add background
            if (GAME_CONFIG.background) {
                const bg = PIXI.Sprite.from(GAME_CONFIG.background);
                bg.width = app.screen.width;
                bg.height = app.screen.height;
                app.stage.addChild(bg);
            }
            
            // Create symbol grid
            createSymbolGrid();
            
            // Add frame
            if (GAME_CONFIG.frame) {
                const frameSprite = PIXI.Sprite.from(GAME_CONFIG.frame);
                frameSprite.width = app.screen.width;
                frameSprite.height = app.screen.height;
                app.stage.addChild(frameSprite);
            }
        }
        
        // Create symbol grid
        function createSymbolGrid() {
            const gridContainer = new PIXI.Container();
            
            // Calculate grid dimensions
            const gridWidth = app.screen.width * 0.6;
            const gridHeight = app.screen.height * 0.6;
            const symbolWidth = gridWidth / GAME_CONFIG.reels;
            const symbolHeight = gridHeight / GAME_CONFIG.rows;
            
            // Position grid in center
            gridContainer.x = (app.screen.width - gridWidth) / 2;
            gridContainer.y = (app.screen.height - gridHeight) / 2;
            
            // Create symbols
            for (let col = 0; col < GAME_CONFIG.reels; col++) {
                for (let row = 0; row < GAME_CONFIG.rows; row++) {
                    const symbolIndex = Math.floor(Math.random() * GAME_CONFIG.symbols.length);
                    const symbolUrl = GAME_CONFIG.symbols[symbolIndex];
                    
                    if (symbolUrl) {
                        const symbol = PIXI.Sprite.from(symbolUrl);
                        symbol.width = symbolWidth * 0.9;
                        symbol.height = symbolHeight * 0.9;
                        symbol.x = col * symbolWidth + symbolWidth * 0.05;
                        symbol.y = row * symbolHeight + symbolHeight * 0.05;
                        symbol.anchor.set(0);
                        
                        gridContainer.addChild(symbol);
                    }
                }
            }
            
            app.stage.addChild(gridContainer);
            window.symbolGrid = gridContainer; // Store reference for animations
        }
        
        // Setup event listeners
        function setupEventListeners() {
            // Spin button
            spinBtn.addEventListener('click', handleSpin);
            
            // Auto play button
            autoBtn.addEventListener('click', handleAutoplay);
            
            // Bet controls
            betUpBtn.addEventListener('click', () => changeBet(1));
            betDownBtn.addEventListener('click', () => changeBet(-1));
            
            // Resize handler
            window.addEventListener('resize', handleResize);
        }
        
        // Handle spin
        function handleSpin() {
            if (gameState.isSpinning || gameState.balance < gameState.bet) {
                return;
            }
            
            console.log('🎰 Starting spin...');
            
            gameState.isSpinning = true;
            gameState.balance -= gameState.bet;
            gameState.win = 0;
            
            updateUI();
            animateSpin();
        }
        
        // Animate spin
        function animateSpin() {
            const symbolGrid = window.symbolGrid;
            if (!symbolGrid) return;
            
            // Simple spin animation
            gsap.to(symbolGrid.children, {
                rotation: Math.PI * 4,
                duration: 2,
                ease: "power2.out",
                stagger: 0.1,
                onComplete: () => {
                    calculateWin();
                    gameState.isSpinning = false;
                    updateUI();
                }
            });
        }
        
        // Calculate win
        function calculateWin() {
            const winChance = Math.random();
            let winAmount = 0;
            
            if (winChance > 0.7) { // 30% win chance
                if (winChance > 0.95) {
                    winAmount = gameState.bet * (10 + Math.floor(Math.random() * 40));
                } else if (winChance > 0.85) {
                    winAmount = gameState.bet * (3 + Math.floor(Math.random() * 7));
                } else {
                    winAmount = gameState.bet * (1 + Math.floor(Math.random() * 2));
                }
            }
            
            gameState.win = winAmount;
            gameState.balance += winAmount;
            
            gameState.spinHistory.push({
                bet: gameState.bet,
                win: winAmount,
                timestamp: Date.now()
            });
            
            console.log(\`🎲 Spin result: bet \${gameState.bet}, win \${winAmount}\`);
        }
        
        // Handle autoplay
        function handleAutoplay() {
            gameState.isAutoplayActive = !gameState.isAutoplayActive;
            
            if (gameState.isAutoplayActive) {
                gameState.autoplayCount = 10;
                autoBtn.textContent = 'STOP';
                autoBtn.classList.add('active');
                autoSpin();
            } else {
                gameState.autoplayCount = 0;
                autoBtn.textContent = 'AUTO';
                autoBtn.classList.remove('active');
            }
        }
        
        // Auto spin
        function autoSpin() {
            if (!gameState.isAutoplayActive || gameState.autoplayCount <= 0) {
                gameState.isAutoplayActive = false;
                autoBtn.textContent = 'AUTO';
                autoBtn.classList.remove('active');
                return;
            }
            
            handleSpin();
            gameState.autoplayCount--;
            
            setTimeout(() => {
                if (gameState.isAutoplayActive) {
                    autoSpin();
                }
            }, 3000);
        }
        
        // Change bet
        function changeBet(direction) {
            const increment = 0.2;
            const newBet = gameState.bet + (direction * increment);
            
            if (newBet >= ${minBet} && newBet <= ${maxBet}) {
                gameState.bet = Math.round(newBet * 100) / 100;
                updateUI();
            }
        }
        
        // Update UI
        function updateUI() {
            balanceEl.textContent = gameState.balance.toFixed(2);
            winEl.textContent = gameState.win.toFixed(2);
            betAmountEl.textContent = gameState.bet.toFixed(2);
            
            spinBtn.disabled = gameState.isSpinning || gameState.balance < gameState.bet;
            spinBtn.textContent = gameState.isSpinning ? 'SPINNING...' : 'SPIN';
        }
        
        // Handle resize
        function handleResize() {
            if (app) {
                app.renderer.resize(canvas.offsetWidth, canvas.offsetHeight);
                renderSlotMachine();
            }
        }
        
        // Initialize game when page loads
        window.addEventListener('load', initGame);
        
        ${customJS}
        
        ${includeAnalytics ? `
        // Analytics tracking
        function trackEvent(event, data) {
            console.log('📊 Game Event:', event, data);
            // Add your analytics code here (Google Analytics, etc.)
        }
        ` : ''}
    </script>
</head>
<body>
    <div class="game-container">
        <div class="slot-machine">
            <canvas id="gameCanvas" class="game-canvas"></canvas>
            
            <div class="ui-overlay">
                <div class="game-info">
                    <div>Balance: $<span id="balance">${initialBalance.toFixed(2)}</span></div>
                    <div>Win: $<span id="win">0.00</span></div>
                </div>
                
                <div class="game-controls">
                    <div class="bet-controls">
                        <button class="bet-btn" id="betDown">-</button>
                        <span>$<span id="betAmount">${minBet.toFixed(2)}</span></span>
                        <button class="bet-btn" id="betUp">+</button>
                    </div>
                    
                    <button class="btn btn-auto" id="autoBtn">AUTO</button>
                    <button class="btn btn-spin" id="spinBtn">SPIN</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
};

/**
 * Download game as HTML file
 */
export const downloadGameAsHTML = async (options: ExportOptions): Promise<void> => {
  const result = await exportGameAsHTML(options);
  
  if (result.success && result.htmlContent) {
    const blob = new Blob([result.htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${options.gameId}-standalone.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('✅ [GameExporter] HTML file downloaded');
  } else {
    console.error('❌ [GameExporter] Failed to export game:', result.error);
    throw new Error(result.error || 'Export failed');
  }
};
