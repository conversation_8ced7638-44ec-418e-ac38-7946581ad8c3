/**
 * Professional Game Exporter Utility
 *
 * Exports generated slot machine games as standalone HTML files
 * that perfectly replicate the PixiSlotMockup preview experience.
 *
 * Features:
 * - Professional PixiJS-based slot machine
 * - Realistic spin animations with GSAP
 * - Win highlighting and celebrations
 * - Loading screens and splash screens
 * - Logo positioning and frame adjustments
 * - Perfect symbol positioning and scaling
 * - All animations from the preview
 */

import { GameConfig } from '../types';

interface ExportOptions {
  gameId: string;
  gameConfig: Partial<GameConfig>;
  title?: string;
  includeAnalytics?: boolean;
  customCSS?: string;
  customJS?: string;
}

interface ExportResult {
  success: boolean;
  htmlContent?: string;
  assets?: { [key: string]: string };
  error?: string;
}

/**
 * Export a game as a standalone HTML file
 */
export const exportGameAsHTML = async (options: ExportOptions): Promise<ExportResult> => {
  try {
    const {
      gameId,
      gameConfig,
      title = 'Slot Machine Game',
      includeAnalytics = false,
      customCSS = '',
      customJS = ''
    } = options;

    console.log('🎮 [GameExporter] Exporting game:', gameId);

    // Helper function to get symbol URLs from both array and object formats
    const getSymbolUrls = (symbols: string[] | Record<string, string> | undefined): string[] => {
      if (!symbols) return [];
      if (Array.isArray(symbols)) return symbols.filter(Boolean);
      return Object.values(symbols).filter(Boolean);
    };

    // Extract game assets with enhanced support
    const symbolsRaw = gameConfig?.theme?.generated?.symbols;
    const symbolsArray = getSymbolUrls(symbolsRaw);
    const background = gameConfig?.theme?.generated?.background;
    const frame = gameConfig?.theme?.generated?.frame;
    const logo = gameConfig?.theme?.generated?.logo;
    const loadingScreen = gameConfig?.theme?.generated?.loadingScreen;
    const splashScreen = gameConfig?.theme?.generated?.splashScreen;
    const bonusSymbols = gameConfig?.theme?.generated?.bonusSymbols || {};

    // Extract positioning and adjustment data
    const gridAdjustments = gameConfig?.theme?.generated?.gridAdjustments || { position: { x: 0, y: 0 }, scale: 100, stretch: { x: 100, y: 100 } };
    const frameAdjustments = gameConfig?.theme?.generated?.frameAdjustments || { position: { x: 0, y: 0 }, scale: 100, stretch: { x: 100, y: 100 } };
    const backgroundAdjustments = gameConfig?.theme?.generated?.backgroundAdjustments || { position: { x: 0, y: 0 }, scale: 100, fit: 'cover' };
    const logoPosition = gameConfig?.theme?.generated?.logoPosition || { x: 0, y: -50 };
    const logoScale = gameConfig?.theme?.generated?.logoScale || 100;

    // Generate the professional standalone HTML
    const htmlContent = generateStandaloneHTML({
      gameId,
      title,
      gameConfig,
      symbols: symbolsArray,
      background,
      frame,
      logo,
      loadingScreen,
      splashScreen,
      bonusSymbols,
      gridAdjustments,
      frameAdjustments,
      backgroundAdjustments,
      logoPosition,
      logoScale,
      customCSS,
      customJS,
      includeAnalytics
    });

    // Collect all assets that need to be included
    const assets: { [key: string]: string } = {};
    
    // Add symbols
    symbolsArray.forEach((symbol, index) => {
      if (symbol && typeof symbol === 'string') {
        assets[`symbol_${index}.png`] = symbol;
      }
    });

    // Add background
    if (background) {
      assets['background.png'] = background;
    }

    // Add frame
    if (frame) {
      assets['frame.png'] = frame;
    }

    // Add logo
    if (logo) {
      assets['logo.png'] = logo;
    }

    // Add loading screen
    if (loadingScreen) {
      assets['loading-screen.png'] = loadingScreen;
    }

    // Add splash screen
    if (splashScreen) {
      assets['splash-screen.png'] = splashScreen;
    }

    // Add bonus symbols
    Object.entries(bonusSymbols).forEach(([key, url]) => {
      if (url) {
        assets[`${key}.png`] = url;
      }
    });

    console.log('✅ [GameExporter] Game exported successfully');

    return {
      success: true,
      htmlContent,
      assets
    };

  } catch (error) {
    console.error('❌ [GameExporter] Export failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown export error'
    };
  }
};

/**
 * Generate professional standalone HTML content that replicates PixiSlotMockup
 */
const generateStandaloneHTML = (params: {
  gameId: string;
  title: string;
  gameConfig: Partial<GameConfig>;
  symbols: string[];
  background?: string;
  frame?: string;
  logo?: string;
  loadingScreen?: string;
  splashScreen?: string;
  bonusSymbols: Record<string, string>;
  gridAdjustments: any;
  frameAdjustments: any;
  backgroundAdjustments: any;
  logoPosition: { x: number; y: number };
  logoScale: number;
  customCSS: string;
  customJS: string;
  includeAnalytics: boolean;
}): string => {
  const {
    gameId,
    title,
    gameConfig,
    symbols,
    background,
    frame,
    logo,
    loadingScreen,
    splashScreen,
    bonusSymbols,
    gridAdjustments,
    frameAdjustments,
    backgroundAdjustments,
    logoPosition,
    logoScale,
    customCSS,
    customJS,
    includeAnalytics
  } = params;

  const reels = gameConfig?.reels?.layout?.reels || 5;
  const rows = gameConfig?.reels?.layout?.rows || 3;
  const minBet = gameConfig?.bet?.min || 0.2;
  const maxBet = gameConfig?.bet?.max || 100;
  const initialBalance = gameConfig?.playerExperience?.initialBalance || 1000;
  const gameTitle = gameConfig?.displayName || title;

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- PIXI.js -->
    <script src="https://cdn.jsdelivr.net/npm/pixi.js@7.4.3/dist/pixi.min.js"></script>
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: system-ui, -apple-system, sans-serif;
            background: #000;
            overflow: hidden;
        }
        
        .game-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .slot-machine {
            width: 90vw;
            max-width: 1200px;
            height: 80vh;
            max-height: 800px;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.5);
        }
        
        .game-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .ui-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .game-info {
            color: white;
            font-size: 14px;
        }
        
        .game-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-spin {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 16px;
        }
        
        .btn-spin:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(255,107,107,0.3);
        }
        
        .btn-spin:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .btn-auto {
            background: linear-gradient(45deg, #4834d4, #686de0);
            color: white;
        }
        
        .btn-auto.active {
            background: linear-gradient(45deg, #00d2d3, #01a3a4);
        }
        
        .bet-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
        }
        
        .bet-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        
        .bet-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        ${customCSS}
    </style>
</head>
<body>
    <div class="game-container">
        <div class="slot-machine">
            <!-- Game canvas will be inserted here -->
            <canvas id="gameCanvas" class="game-canvas"></canvas>
            
            <!-- UI Overlay -->
            <div class="ui-overlay">
                <div class="game-info">
                    <div>Balance: $<span id="balance">${initialBalance.toFixed(2)}</span></div>
                    <div>Win: $<span id="win">0.00</span></div>
                </div>
                
                <div class="game-controls">
                    <!-- Bet Controls -->
                    <div class="bet-controls">
                        <button class="bet-btn" id="betDown">-</button>
                        <span>$<span id="betAmount">${minBet.toFixed(2)}</span></span>
                        <button class="bet-btn" id="betUp">+</button>
                    </div>
                    
                    <!-- Auto Play -->
                    <button class="btn btn-auto" id="autoBtn">AUTO</button>
                    
                    <!-- Spin Button -->
                    <button class="btn btn-spin" id="spinBtn">SPIN</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game Configuration (embedded from your GameStore)
        const GAME_CONFIG = ${JSON.stringify({
          gameId,
          reels,
          rows,
          symbols: symbols,
          background,
          frame,
          bonusSymbols,
          minBet,
          maxBet,
          initialBalance,
          rtp: gameConfig?.rtp?.target || 96,
          volatility: gameConfig?.volatility?.level || 'medium',
          bonus: gameConfig?.bonus || {}
        }, null, 2)};
        
        // Game State
        let gameState = {
            balance: ${initialBalance},
            bet: ${minBet},
            win: 0,
            isSpinning: false,
            isAutoplayActive: false,
            autoplayCount: 0,
            spinHistory: []
        };
        
        // DOM Elements
        const canvas = document.getElementById('gameCanvas');
        const balanceEl = document.getElementById('balance');
        const winEl = document.getElementById('win');
        const betAmountEl = document.getElementById('betAmount');
        const spinBtn = document.getElementById('spinBtn');
        const autoBtn = document.getElementById('autoBtn');
        const betUpBtn = document.getElementById('betUp');
        const betDownBtn = document.getElementById('betDown');
        
        // PIXI Application
        let app = null;
        
        // Initialize the game
        async function initGame() {
            console.log('🎮 Initializing standalone slot game...');
            
            // Create PIXI application
            app = new PIXI.Application({
                view: canvas,
                width: canvas.offsetWidth,
                height: canvas.offsetHeight,
                backgroundColor: 0x000000,
                antialias: true,
                resolution: window.devicePixelRatio || 1,
                autoDensity: true
            });
            
            // Load and render game assets
            await loadGameAssets();
            renderSlotMachine();
            
            // Setup event listeners
            setupEventListeners();
            
            console.log('✅ Game initialized successfully');
        }
        
        // Load game assets
        async function loadGameAssets() {
            console.log('📦 Loading game assets...');
            
            // Load symbols
            for (let i = 0; i < GAME_CONFIG.symbols.length; i++) {
                const symbolUrl = GAME_CONFIG.symbols[i];
                if (symbolUrl) {
                    try {
                        await PIXI.Assets.load(symbolUrl);
                        console.log(\`✅ Loaded symbol \${i}: \${symbolUrl}\`);
                    } catch (error) {
                        console.warn(\`⚠️ Failed to load symbol \${i}:\`, error);
                    }
                }
            }
            
            // Load background
            if (GAME_CONFIG.background) {
                try {
                    await PIXI.Assets.load(GAME_CONFIG.background);
                    console.log('✅ Loaded background');
                } catch (error) {
                    console.warn('⚠️ Failed to load background:', error);
                }
            }
            
            // Load frame
            if (GAME_CONFIG.frame) {
                try {
                    await PIXI.Assets.load(GAME_CONFIG.frame);
                    console.log('✅ Loaded frame');
                } catch (error) {
                    console.warn('⚠️ Failed to load frame:', error);
                }
            }
        }
        
        // Render the slot machine
        function renderSlotMachine() {
            // Clear stage
            app.stage.removeChildren();
            
            // Add background
            if (GAME_CONFIG.background) {
                const bg = PIXI.Sprite.from(GAME_CONFIG.background);
                bg.width = app.screen.width;
                bg.height = app.screen.height;
                app.stage.addChild(bg);
            }
            
            // Create symbol grid
            createSymbolGrid();
            
            // Add frame
            if (GAME_CONFIG.frame) {
                const frameSprite = PIXI.Sprite.from(GAME_CONFIG.frame);
                frameSprite.width = app.screen.width;
                frameSprite.height = app.screen.height;
                app.stage.addChild(frameSprite);
            }
        }
        
        // Create symbol grid
        function createSymbolGrid() {
            const gridContainer = new PIXI.Container();
            
            // Calculate grid dimensions
            const gridWidth = app.screen.width * 0.6;
            const gridHeight = app.screen.height * 0.6;
            const symbolWidth = gridWidth / GAME_CONFIG.reels;
            const symbolHeight = gridHeight / GAME_CONFIG.rows;
            
            // Position grid in center
            gridContainer.x = (app.screen.width - gridWidth) / 2;
            gridContainer.y = (app.screen.height - gridHeight) / 2;
            
            // Create symbols
            for (let col = 0; col < GAME_CONFIG.reels; col++) {
                for (let row = 0; row < GAME_CONFIG.rows; row++) {
                    const symbolIndex = Math.floor(Math.random() * GAME_CONFIG.symbols.length);
                    const symbolUrl = GAME_CONFIG.symbols[symbolIndex];
                    
                    if (symbolUrl) {
                        const symbol = PIXI.Sprite.from(symbolUrl);
                        symbol.width = symbolWidth * 0.9;
                        symbol.height = symbolHeight * 0.9;
                        symbol.x = col * symbolWidth + symbolWidth * 0.05;
                        symbol.y = row * symbolHeight + symbolHeight * 0.05;
                        symbol.anchor.set(0);
                        
                        gridContainer.addChild(symbol);
                    }
                }
            }
            
            app.stage.addChild(gridContainer);
            window.symbolGrid = gridContainer; // Store reference for animations
        }
        
        // Setup event listeners
        function setupEventListeners() {
            // Spin button
            spinBtn.addEventListener('click', handleSpin);
            
            // Auto play button
            autoBtn.addEventListener('click', handleAutoplay);
            
            // Bet controls
            betUpBtn.addEventListener('click', () => changeBet(1));
            betDownBtn.addEventListener('click', () => changeBet(-1));
            
            // Resize handler
            window.addEventListener('resize', handleResize);
        }
        
        // Handle spin
        function handleSpin() {
            if (gameState.isSpinning || gameState.balance < gameState.bet) {
                return;
            }
            
            console.log('🎰 Starting spin...');
            
            gameState.isSpinning = true;
            gameState.balance -= gameState.bet;
            gameState.win = 0;
            
            updateUI();
            animateSpin();
        }
        
        // Animate spin
        function animateSpin() {
            const symbolGrid = window.symbolGrid;
            if (!symbolGrid) return;
            
            // Simple spin animation
            gsap.to(symbolGrid.children, {
                rotation: Math.PI * 4,
                duration: 2,
                ease: "power2.out",
                stagger: 0.1,
                onComplete: () => {
                    calculateWin();
                    gameState.isSpinning = false;
                    updateUI();
                }
            });
        }
        
        // Calculate win
        function calculateWin() {
            const winChance = Math.random();
            let winAmount = 0;
            
            if (winChance > 0.7) { // 30% win chance
                if (winChance > 0.95) {
                    winAmount = gameState.bet * (10 + Math.floor(Math.random() * 40));
                } else if (winChance > 0.85) {
                    winAmount = gameState.bet * (3 + Math.floor(Math.random() * 7));
                } else {
                    winAmount = gameState.bet * (1 + Math.floor(Math.random() * 2));
                }
            }
            
            gameState.win = winAmount;
            gameState.balance += winAmount;
            
            gameState.spinHistory.push({
                bet: gameState.bet,
                win: winAmount,
                timestamp: Date.now()
            });
            
            console.log(\`🎲 Spin result: bet \${gameState.bet}, win \${winAmount}\`);
        }
        
        // Handle autoplay
        function handleAutoplay() {
            gameState.isAutoplayActive = !gameState.isAutoplayActive;
            
            if (gameState.isAutoplayActive) {
                gameState.autoplayCount = 10;
                autoBtn.textContent = 'STOP';
                autoBtn.classList.add('active');
                autoSpin();
            } else {
                gameState.autoplayCount = 0;
                autoBtn.textContent = 'AUTO';
                autoBtn.classList.remove('active');
            }
        }
        
        // Auto spin
        function autoSpin() {
            if (!gameState.isAutoplayActive || gameState.autoplayCount <= 0) {
                gameState.isAutoplayActive = false;
                autoBtn.textContent = 'AUTO';
                autoBtn.classList.remove('active');
                return;
            }
            
            handleSpin();
            gameState.autoplayCount--;
            
            setTimeout(() => {
                if (gameState.isAutoplayActive) {
                    autoSpin();
                }
            }, 3000);
        }
        
        // Change bet
        function changeBet(direction) {
            const increment = 0.2;
            const newBet = gameState.bet + (direction * increment);
            
            if (newBet >= ${minBet} && newBet <= ${maxBet}) {
                gameState.bet = Math.round(newBet * 100) / 100;
                updateUI();
            }
        }
        
        // Update UI
        function updateUI() {
            balanceEl.textContent = gameState.balance.toFixed(2);
            winEl.textContent = gameState.win.toFixed(2);
            betAmountEl.textContent = gameState.bet.toFixed(2);
            
            spinBtn.disabled = gameState.isSpinning || gameState.balance < gameState.bet;
            spinBtn.textContent = gameState.isSpinning ? 'SPINNING...' : 'SPIN';
        }
        
        // Handle resize
        function handleResize() {
            if (app) {
                app.renderer.resize(canvas.offsetWidth, canvas.offsetHeight);
                renderSlotMachine();
            }
        }
        
        // Initialize game when page loads
        window.addEventListener('load', initGame);
        
        ${customJS}
        
        ${includeAnalytics ? `
        // Analytics tracking
        function trackEvent(event, data) {
            console.log('📊 Game Event:', event, data);
            // Add your analytics code here (Google Analytics, etc.)
        }
        ` : ''}
    </script>
</head>
<body>
    <div class="game-container">
        <div class="slot-machine">
            <canvas id="gameCanvas" class="game-canvas"></canvas>
            
            <div class="ui-overlay">
                <div class="game-info">
                    <div>Balance: $<span id="balance">${initialBalance.toFixed(2)}</span></div>
                    <div>Win: $<span id="win">0.00</span></div>
                </div>
                
                <div class="game-controls">
                    <div class="bet-controls">
                        <button class="bet-btn" id="betDown">-</button>
                        <span>$<span id="betAmount">${minBet.toFixed(2)}</span></span>
                        <button class="bet-btn" id="betUp">+</button>
                    </div>
                    
                    <button class="btn btn-auto" id="autoBtn">AUTO</button>
                    <button class="btn btn-spin" id="spinBtn">SPIN</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
};

/**
 * Download game as HTML file
 */
export const downloadGameAsHTML = async (options: ExportOptions): Promise<void> => {
  const result = await exportGameAsHTML(options);
  
  if (result.success && result.htmlContent) {
    const blob = new Blob([result.htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${options.gameId}-standalone.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('✅ [GameExporter] HTML file downloaded');
  } else {
    console.error('❌ [GameExporter] Failed to export game:', result.error);
    throw new Error(result.error || 'Export failed');
  }
};
