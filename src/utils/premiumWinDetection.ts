/**
 * Premium Win Detection System
 * Multi-payline, cascading wins, bonus features
 */

export interface PaylineConfig {
  id: number;
  name: string;
  positions: Array<{ reel: number; row: number }>;
  active: boolean;
}

export interface WinResult {
  paylineId: number;
  symbol: string;
  matchCount: number;
  winAmount: number;
  positions: Array<{ reel: number; row: number }>;
  winType: 'small' | 'big' | 'mega' | 'jackpot';
  multiplier: number;
}

export interface PremiumWinDetection {
  totalWin: number;
  wins: WinResult[];
  cascadeWins: WinResult[][];
  bonusTriggered: boolean;
  freeSpinsTriggered: boolean;
  jackpotTriggered: boolean;
}

// Standard 25-payline configuration for 5x3 grid
export const PREMIUM_PAYLINES: PaylineConfig[] = [
  // Horizontal lines
  { id: 1, name: "Top Line", positions: [{reel:0,row:0},{reel:1,row:0},{reel:2,row:0},{reel:3,row:0},{reel:4,row:0}], active: true },
  { id: 2, name: "Middle Line", positions: [{reel:0,row:1},{reel:1,row:1},{reel:2,row:1},{reel:3,row:1},{reel:4,row:1}], active: true },
  { id: 3, name: "Bottom Line", positions: [{reel:0,row:2},{reel:1,row:2},{reel:2,row:2},{reel:3,row:2},{reel:4,row:2}], active: true },
  
  // Diagonal lines
  { id: 4, name: "Top-Bottom Diagonal", positions: [{reel:0,row:0},{reel:1,row:1},{reel:2,row:2},{reel:3,row:1},{reel:4,row:0}], active: true },
  { id: 5, name: "Bottom-Top Diagonal", positions: [{reel:0,row:2},{reel:1,row:1},{reel:2,row:0},{reel:3,row:1},{reel:4,row:2}], active: true },
  
  // V-shapes and inverted V-shapes
  { id: 6, name: "V-Shape", positions: [{reel:0,row:0},{reel:1,row:1},{reel:2,row:2},{reel:3,row:2},{reel:4,row:2}], active: true },
  { id: 7, name: "Inverted V", positions: [{reel:0,row:2},{reel:1,row:1},{reel:2,row:0},{reel:3,row:0},{reel:4,row:0}], active: true },
  
  // Zigzag patterns
  { id: 8, name: "Zigzag 1", positions: [{reel:0,row:1},{reel:1,row:0},{reel:2,row:1},{reel:3,row:2},{reel:4,row:1}], active: true },
  { id: 9, name: "Zigzag 2", positions: [{reel:0,row:1},{reel:1,row:2},{reel:2,row:1},{reel:3,row:0},{reel:4,row:1}], active: true },
  
  // Additional premium patterns (10-25)
  { id: 10, name: "W-Shape", positions: [{reel:0,row:0},{reel:1,row:2},{reel:2,row:1},{reel:3,row:2},{reel:4,row:0}], active: true },
  // ... more paylines
];

export class PremiumWinDetector {
  private paytable: Map<string, number[]> = new Map([
    ['wild', [0, 0, 50, 250, 1000]],
    ['scatter', [0, 2, 5, 20, 100]], // Scatter pays anywhere
    ['high_1', [0, 0, 10, 40, 150]],
    ['high_2', [0, 0, 8, 30, 120]],
    ['high_3', [0, 0, 6, 25, 100]],
    ['high_4', [0, 0, 5, 20, 80]],
    ['medium_1', [0, 0, 4, 15, 60]],
    ['medium_2', [0, 0, 3, 12, 50]],
    ['medium_3', [0, 0, 3, 10, 40]],
    ['medium_4', [0, 0, 2, 8, 30]],
    ['low_1', [0, 0, 2, 6, 25]],
    ['low_2', [0, 0, 2, 5, 20]],
    ['low_3', [0, 0, 1, 4, 15]],
    ['low_4', [0, 0, 1, 3, 10]]
  ]);

  detectWins(grid: string[][], bet: number, activePaylines: number[] = []): PremiumWinDetection {
    const allWins: WinResult[] = [];
    let totalWin = 0;
    let bonusTriggered = false;
    let freeSpinsTriggered = false;
    let jackpotTriggered = false;

    // Use all paylines if none specified
    const paylinesToCheck = activePaylines.length > 0 
      ? PREMIUM_PAYLINES.filter(p => activePaylines.includes(p.id))
      : PREMIUM_PAYLINES.filter(p => p.active);

    // Check each payline
    for (const payline of paylinesToCheck) {
      const win = this.checkPaylineWin(grid, payline, bet);
      if (win) {
        allWins.push(win);
        totalWin += win.winAmount;

        // Check for bonus triggers
        if (win.symbol === 'scatter' && win.matchCount >= 3) {
          freeSpinsTriggered = true;
        }
        if (win.symbol === 'wild' && win.matchCount === 5) {
          jackpotTriggered = true;
        }
      }
    }

    // Check for scatter pays (anywhere on reels)
    const scatterWin = this.checkScatterPay(grid, bet);
    if (scatterWin) {
      allWins.push(scatterWin);
      totalWin += scatterWin.winAmount;
      if (scatterWin.matchCount >= 3) {
        freeSpinsTriggered = true;
      }
    }

    return {
      totalWin,
      wins: allWins,
      cascadeWins: [], // For future cascade feature
      bonusTriggered,
      freeSpinsTriggered,
      jackpotTriggered
    };
  }

  private checkPaylineWin(grid: string[][], payline: PaylineConfig, bet: number): WinResult | null {
    const symbols = payline.positions.map(pos => grid[pos.reel]?.[pos.row]).filter(Boolean);
    if (symbols.length === 0) return null;

    const firstSymbol = symbols[0];
    let matchCount = 1;
    const winPositions = [payline.positions[0]];

    // Check consecutive matches with wild substitution
    for (let i = 1; i < symbols.length; i++) {
      const currentSymbol = symbols[i];
      if (currentSymbol === firstSymbol || 
          currentSymbol === 'wild' || 
          firstSymbol === 'wild') {
        matchCount++;
        winPositions.push(payline.positions[i]);
      } else {
        break;
      }
    }

    // Minimum 3 symbols for win
    if (matchCount < 3) return null;

    const baseSymbol = firstSymbol === 'wild' ? symbols.find(s => s !== 'wild') || 'wild' : firstSymbol;
    const payout = this.paytable.get(baseSymbol)?.[matchCount] || 0;
    
    if (payout === 0) return null;

    let winAmount = bet * payout;
    let multiplier = 1;

    // Wild multiplier bonus
    const wildCount = symbols.slice(0, matchCount).filter(s => s === 'wild').length;
    if (wildCount > 0) {
      multiplier = Math.pow(2, wildCount); // 2x per wild
      winAmount *= multiplier;
    }

    const winType = this.determineWinType(winAmount, bet);

    return {
      paylineId: payline.id,
      symbol: baseSymbol,
      matchCount,
      winAmount,
      positions: winPositions,
      winType,
      multiplier
    };
  }

  private checkScatterPay(grid: string[][], bet: number): WinResult | null {
    const scatterPositions: Array<{ reel: number; row: number }> = [];
    
    // Count scatters anywhere on reels
    for (let reel = 0; reel < grid.length; reel++) {
      for (let row = 0; row < grid[reel].length; row++) {
        if (grid[reel][row] === 'scatter') {
          scatterPositions.push({ reel, row });
        }
      }
    }

    if (scatterPositions.length < 3) return null;

    const matchCount = scatterPositions.length;
    const payout = this.paytable.get('scatter')?.[Math.min(matchCount, 4)] || 0;
    const winAmount = bet * payout;

    return {
      paylineId: 0, // Special payline for scatters
      symbol: 'scatter',
      matchCount,
      winAmount,
      positions: scatterPositions,
      winType: this.determineWinType(winAmount, bet),
      multiplier: 1
    };
  }

  private determineWinType(winAmount: number, bet: number): 'small' | 'big' | 'mega' | 'jackpot' {
    const ratio = winAmount / bet;
    if (ratio >= 1000) return 'jackpot';
    if (ratio >= 100) return 'mega';
    if (ratio >= 25) return 'big';
    return 'small';
  }
}