import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useGameStore } from '../../store';
import PixiSlotMockup from '../mockups/PixiSlotMockup';
import SlotGameUI from '../visual-journey/slot-animation/SlotGameUI';
import MobileLandscapeUI from '../visual-journey/slot-animation/MobileLandscapeUI';
import MobilePortraitUI from '../visual-journey/slot-animation/MobilePortraitUI';

interface StandaloneGamePlayerProps {
  gameConfig?: any; // Optional: Load specific game config
  className?: string;
  viewMode?: 'desktop' | 'mobile-portrait' | 'mobile-landscape';
  onGameEvent?: (event: string, data: any) => void;
}

/**
 * Standalone Game Player - A complete, playable slot machine
 * 
 * Uses your existing:
 * - GameStore for game configuration
 * - PixiSlotMockup for rendering (no changes to your working preview)
 * - UI components for controls
 * 
 * Adds real game functionality:
 * - Balance management
 * - Betting system
 * - Win calculation
 * - Bonus features
 * - Game state persistence
 */
const StandaloneGamePlayer: React.FC<StandaloneGamePlayerProps> = ({
  gameConfig,
  className = '',
  viewMode = 'desktop',
  onGameEvent
}) => {
  const { config, updateConfig } = useGameStore();
  
  // Game State (separate from GameStore to avoid conflicts)
  const [gameState, setGameState] = useState({
    balance: 1000,
    bet: 1,
    win: 0,
    totalWin: 0,
    isSpinning: false,
    isAutoplayActive: false,
    autoplayCount: 0,
    gameHistory: [] as any[],
    bonusActive: false,
    freeSpinsRemaining: 0,
    lastSpinResult: null as any,
    winLines: [] as any[],
    currentMultiplier: 1
  });

  // Helper function to get symbol URLs from both array and object formats (same as other components)
  const getSymbolUrls = useCallback((symbols: string[] | Record<string, string> | undefined): string[] => {
    if (!symbols) return [];
    if (Array.isArray(symbols)) return symbols.filter(Boolean); // Filter out empty strings
    return Object.values(symbols).filter(Boolean); // Filter out empty strings
  }, []);

  // Use specific selectors to ensure reactivity
  const storeSymbols = useGameStore((state) => state.config?.theme?.generated?.symbols);

  // Game Configuration (from GameStore or props)
  const activeConfig = gameConfig || config;
  const reels = activeConfig?.reels?.layout?.reels || 5;
  const rows = activeConfig?.reels?.layout?.rows || 3;
  const symbolsRaw = storeSymbols || activeConfig?.theme?.generated?.symbols;
  const symbols = getSymbolUrls(symbolsRaw);
  const background = activeConfig?.theme?.generated?.background;
  const frame = activeConfig?.theme?.generated?.frame;
  const bonusSymbols = activeConfig?.theme?.generated?.bonusSymbols || {};

  // Debug logging for StandaloneGamePlayer
  React.useEffect(() => {
    console.log('🎮 [StandaloneGamePlayer] Symbols check:');
    console.log('🎮 [StandaloneGamePlayer] Store symbols:', storeSymbols);
    console.log('🎮 [StandaloneGamePlayer] Config symbols:', activeConfig?.theme?.generated?.symbols);
    console.log('🎮 [StandaloneGamePlayer] Final symbols:', symbolsRaw);
    console.log('🎮 [StandaloneGamePlayer] Processed symbols:', symbols);
    console.log('🎮 [StandaloneGamePlayer] Symbol count:', symbols.length);
  }, [storeSymbols, activeConfig?.theme?.generated?.symbols, symbolsRaw, symbols]);

  // Game Logic References
  const spinTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const autoplayTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize game from config
  useEffect(() => {
    if (activeConfig) {
      console.log('🎮 [StandaloneGamePlayer] Initializing game with config:', activeConfig);
      
      // Set initial balance from config or default
      const initialBalance = activeConfig?.playerExperience?.initialBalance || 1000;
      const initialBet = activeConfig?.bet?.min || 1;
      
      setGameState(prev => ({
        ...prev,
        balance: initialBalance,
        bet: initialBet
      }));

      // Notify parent component
      onGameEvent?.('game:initialized', { config: activeConfig, balance: initialBalance });
    }
  }, [activeConfig, onGameEvent]);

  // Spin Logic - Real slot machine functionality
  const handleSpin = useCallback(() => {
    if (gameState.isSpinning || gameState.balance < gameState.bet) {
      console.log('🚫 [StandaloneGamePlayer] Spin blocked:', { 
        isSpinning: gameState.isSpinning, 
        balance: gameState.balance, 
        bet: gameState.bet 
      });
      return;
    }

    console.log('🎰 [StandaloneGamePlayer] Starting spin with bet:', gameState.bet);

    // Deduct bet from balance
    setGameState(prev => ({
      ...prev,
      isSpinning: true,
      balance: prev.balance - prev.bet,
      win: 0
    }));

    // Trigger PixiSlotMockup spin animation
    window.dispatchEvent(new CustomEvent('pixiSlotSpin', {
      detail: { 
        bet: gameState.bet,
        balance: gameState.balance - gameState.bet,
        source: 'standalone-player'
      }
    }));

    // Simulate spin duration and calculate results
    const spinDuration = 3000; // 3 seconds
    spinTimeoutRef.current = setTimeout(() => {
      calculateSpinResult();
    }, spinDuration);

    onGameEvent?.('spin:start', { bet: gameState.bet, balance: gameState.balance - gameState.bet });
  }, [gameState, onGameEvent]);

  // Enhanced Spin Results with Bonus Features Integration
  const calculateSpinResult = useCallback(() => {
    console.log('🎲 [StandaloneGamePlayer] Calculating spin result...');

    // Get RTP and volatility from config
    const targetRTP = activeConfig?.rtp?.target || 96;
    const volatility = activeConfig?.volatility?.level || 'medium';

    // Adjust win frequency based on volatility
    const winFrequency = volatility === 'low' ? 0.4 : volatility === 'high' ? 0.25 : 0.32;

    const winChance = Math.random();
    let winAmount = 0;
    let winType = 'none';
    let winLines: any[] = [];
    let bonusTriggered = false;
    let bonusType = '';

    // Enhanced win calculation based on RTP
    if (winChance < winFrequency) {
      const winTier = Math.random();

      if (winTier > 0.95) { // 5% mega win
        winAmount = gameState.bet * (50 + Math.floor(Math.random() * 200)); // 50x-250x
        winType = 'mega-win';
        winLines = generateWinLines(5); // 5 win lines
      } else if (winTier > 0.85) { // 10% big win
        winAmount = gameState.bet * (10 + Math.floor(Math.random() * 40)); // 10x-50x
        winType = 'big-win';
        winLines = generateWinLines(3); // 3 win lines
      } else if (winTier > 0.60) { // 25% medium win
        winAmount = gameState.bet * (3 + Math.floor(Math.random() * 7)); // 3x-10x
        winType = 'medium-win';
        winLines = generateWinLines(2); // 2 win lines
      } else { // 60% small win
        winAmount = gameState.bet * (1 + Math.floor(Math.random() * 2)); // 1x-3x
        winType = 'small-win';
        winLines = generateWinLines(1); // 1 win line
      }
    }

    // Enhanced Bonus Features Integration
    const bonusChance = Math.random();

    // Free Spins Bonus
    if (bonusChance > 0.985 && activeConfig?.bonus?.freeSpins?.enabled && hasSymbolType('free')) {
      bonusTriggered = true;
      bonusType = 'freespins';
      const freeSpinsCount = activeConfig.bonus.freeSpins.count || 10;
      const freeSpinsMultiplier = Math.max(...(activeConfig.bonus.freeSpins.multipliers || [1]));

      // Add bonus win on top of regular win
      winAmount += gameState.bet * freeSpinsMultiplier * freeSpinsCount;
      winType = 'freespins';

      setGameState(prev => ({
        ...prev,
        bonusActive: true,
        freeSpinsRemaining: freeSpinsCount,
        currentMultiplier: freeSpinsMultiplier
      }));

      onGameEvent?.('bonus:triggered', { type: 'freespins', count: freeSpinsCount, multiplier: freeSpinsMultiplier });
    }

    // Pick & Click Bonus
    else if (bonusChance > 0.990 && activeConfig?.bonus?.pickAndClick?.enabled && hasSymbolType('bonus')) {
      bonusTriggered = true;
      bonusType = 'pickAndClick';
      const maxPrize = activeConfig.bonus.pickAndClick.maxPrize || 100;
      const picks = activeConfig.bonus.pickAndClick.picks || 3;

      // Simulate pick & click win
      const pickWin = gameState.bet * (Math.floor(Math.random() * maxPrize) + 1);
      winAmount += pickWin;
      winType = 'bonus';

      onGameEvent?.('bonus:triggered', { type: 'pickAndClick', picks, maxPrize, win: pickWin });
    }

    // Wheel Bonus
    else if (bonusChance > 0.992 && activeConfig?.bonus?.wheel?.enabled && hasSymbolType('bonus')) {
      bonusTriggered = true;
      bonusType = 'wheel';
      const maxMultiplier = activeConfig.bonus.wheel.maxMultiplier || 50;
      const wheelWin = gameState.bet * (Math.floor(Math.random() * maxMultiplier) + 1);

      winAmount += wheelWin;
      winType = 'bonus';

      onGameEvent?.('bonus:triggered', { type: 'wheel', multiplier: wheelWin / gameState.bet });
    }

    // Generate spin result grid for display
    const spinResult = generateSpinResultGrid();

    // Update game state with enhanced results
    setGameState(prev => ({
      ...prev,
      isSpinning: false,
      win: winAmount,
      totalWin: prev.totalWin + winAmount,
      balance: prev.balance + winAmount,
      lastSpinResult: spinResult,
      winLines: winLines,
      gameHistory: [...prev.gameHistory, {
        bet: prev.bet,
        win: winAmount,
        winType,
        bonusTriggered,
        bonusType,
        spinResult,
        winLines,
        timestamp: Date.now()
      }]
    }));

    console.log('✅ [StandaloneGamePlayer] Enhanced spin complete:', {
      winAmount,
      winType,
      bonusTriggered,
      bonusType,
      winLines: winLines.length
    });

    // Notify PixiSlotMockup about the win with enhanced data
    if (winAmount > 0) {
      window.dispatchEvent(new CustomEvent('slotWin', {
        detail: {
          winAmount,
          winType,
          multiplier: winAmount / gameState.bet,
          winLines,
          bonusTriggered,
          bonusType
        }
      }));
    }

    onGameEvent?.('spin:complete', {
      win: winAmount,
      winType,
      balance: gameState.balance + winAmount,
      bonusTriggered,
      bonusType,
      winLines
    });

    // Handle autoplay with bonus pause
    if (gameState.isAutoplayActive && gameState.autoplayCount > 1) {
      // Pause autoplay on bonus or big wins to show celebration
      const pauseTime = bonusTriggered || winType === 'big-win' || winType === 'mega-win' ? 4000 : 2000;

      autoplayTimeoutRef.current = setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          autoplayCount: prev.autoplayCount - 1
        }));
        handleSpin();
      }, pauseTime);
    } else if (gameState.isAutoplayActive) {
      setGameState(prev => ({
        ...prev,
        isAutoplayActive: false,
        autoplayCount: 0
      }));
    }
  }, [gameState, activeConfig, onGameEvent, handleSpin]);

  // Autoplay Toggle
  const handleAutoplayToggle = useCallback(() => {
    if (gameState.isAutoplayActive) {
      // Stop autoplay
      setGameState(prev => ({
        ...prev,
        isAutoplayActive: false,
        autoplayCount: 0
      }));
      
      if (autoplayTimeoutRef.current) {
        clearTimeout(autoplayTimeoutRef.current);
        autoplayTimeoutRef.current = null;
      }
    } else {
      // Start autoplay
      const autoplayCount = 10; // Default 10 spins
      setGameState(prev => ({
        ...prev,
        isAutoplayActive: true,
        autoplayCount
      }));
      
      // Start first auto spin
      setTimeout(() => handleSpin(), 1000);
    }
    
    onGameEvent?.('autoplay:toggle', { active: !gameState.isAutoplayActive });
  }, [gameState.isAutoplayActive, handleSpin, onGameEvent]);

  // Bet Change
  const handleBetChange = useCallback((newBet: number) => {
    const minBet = activeConfig?.bet?.min || 0.2;
    const maxBet = activeConfig?.bet?.max || 100;
    const safeBet = Math.max(minBet, Math.min(maxBet, newBet));
    
    setGameState(prev => ({
      ...prev,
      bet: safeBet
    }));
    
    onGameEvent?.('bet:change', { bet: safeBet });
  }, [activeConfig, onGameEvent]);

  // Helper Functions for Enhanced Game Logic

  // Check if game has specific symbol types for bonus features
  const hasSymbolType = useCallback((type: 'wild' | 'scatter' | 'bonus' | 'free') => {
    if (bonusSymbols && typeof bonusSymbols === 'object') {
      return Boolean(bonusSymbols[type] || bonusSymbols[`${type}Symbol`]);
    }
    return false;
  }, [bonusSymbols]);

  // Generate win lines for display
  const generateWinLines = useCallback((count: number) => {
    const lines = [];
    for (let i = 0; i < count; i++) {
      lines.push({
        id: i + 1,
        positions: generateRandomWinLine(),
        symbol: Math.floor(Math.random() * symbols.length),
        multiplier: Math.floor(Math.random() * 5) + 1
      });
    }
    return lines;
  }, [symbols]);

  // Generate random win line positions
  const generateRandomWinLine = useCallback(() => {
    const positions = [];
    for (let col = 0; col < reels; col++) {
      positions.push({
        reel: col,
        row: Math.floor(Math.random() * rows)
      });
    }
    return positions;
  }, [reels, rows]);

  // Generate spin result grid
  const generateSpinResultGrid = useCallback(() => {
    const grid = [];
    for (let col = 0; col < reels; col++) {
      const column = [];
      for (let row = 0; row < rows; row++) {
        column.push({
          symbolIndex: Math.floor(Math.random() * symbols.length),
          symbol: symbols[Math.floor(Math.random() * symbols.length)]
        });
      }
      grid.push(column);
    }
    return grid;
  }, [reels, rows, symbols]);

  // Game State Management (removed localStorage persistence to avoid quota issues)
  // Game state is now only kept in memory during the session

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      if (spinTimeoutRef.current) clearTimeout(spinTimeoutRef.current);
      if (autoplayTimeoutRef.current) clearTimeout(autoplayTimeoutRef.current);
    };
  }, []);

  // Get UI adjustments from config (same as PixiPreviewWrapper)
  const gridAdjustments = {
    position: activeConfig?.gridPosition || { x: 0, y: 0 },
    scale: activeConfig?.gridScale || 100,
    stretch: activeConfig?.gridStretch || { x: 100, y: 100 }
  };

  const frameAdjustments = {
    position: activeConfig?.framePosition || { x: 0, y: 0 },
    scale: activeConfig?.frameScale || 100,
    stretch: activeConfig?.frameStretch || { x: 100, y: 100 }
  };

  const backgroundAdjustments = {
    position: activeConfig?.backgroundPosition || { x: 0, y: 0 },
    scale: activeConfig?.backgroundScale || 100,
    fit: activeConfig?.backgroundFit || 'cover'
  };

  const uiButtonAdjustments = {
    position: activeConfig?.uiButtonPosition || { x: 0, y: 0 },
    scale: activeConfig?.uiButtonScale || 100,
    visibility: activeConfig?.uiButtonVisibility !== false
  };

  return (
    <div className={`standalone-game-player ${className}`}>
      {/* Game Title */}
      <div className="bg-gray-900 px-4 py-3 border-b border-gray-700 flex items-center justify-between rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-gray-300 text-sm font-medium">
            {activeConfig?.displayName || activeConfig?.gameId || 'Standalone Slot Game'}
          </span>
        </div>
        <div className="flex items-center gap-4">
          <div className="text-gray-500 text-xs">
            Balance: ${gameState.balance.toFixed(2)} • Bet: ${gameState.bet.toFixed(2)}
            {gameState.isSpinning && <span className="text-yellow-400 ml-2">• SPINNING</span>}
            {gameState.isAutoplayActive && <span className="text-green-400 ml-2">• AUTO ({gameState.autoplayCount})</span>}
          </div>
        </div>
      </div>

      {/* Game Area */}
      <div className="relative bg-black overflow-hidden" style={{ height: '600px' }}>
        <div className="absolute inset-0 flex items-center justify-center">
          {/* Your existing PixiSlotMockup - NO CHANGES */}
          <PixiSlotMockup
            cols={reels}
            rows={rows}
            symbols={symbols} // Already processed by getSymbolUrls
            background={background}
            frame={frame}
            showControls={false} // We'll use our own UI overlay
            isMobile={viewMode !== 'desktop'}
            orientation={viewMode === 'mobile-landscape' ? 'landscape' : 'portrait'}
            gridAdjustments={gridAdjustments}
            frameAdjustments={frameAdjustments}
            backgroundAdjustments={backgroundAdjustments}
            uiButtonAdjustments={uiButtonAdjustments}
            enableSpinAnimation={true}
            spinTriggerEvent="pixiSlotSpin"
            isSpinning={gameState.isSpinning}
            balance={gameState.balance}
            bet={gameState.bet}
            onSpinComplete={() => {
              console.log('🎰 [StandaloneGamePlayer] PixiSlotMockup spin animation complete');
            }}
          />

          {/* UI Controls Overlay */}
          <div className="absolute bottom-0 left-0 right-0 w-full z-30">
            {viewMode === 'desktop' ? (
              <SlotGameUI
                onSpin={handleSpin}
                onAutoplayToggle={handleAutoplayToggle}
                onMaxBet={() => handleBetChange(activeConfig?.bet?.max || 100)}
                balance={gameState.balance}
                bet={gameState.bet}
                win={gameState.win}
                isSpinning={gameState.isSpinning}
                spinButtonSvg={undefined}
                spinButtonImageUrl={undefined}
                customButtons={undefined}
                className="shadow-lg border-t border-gray-700/50 h-[72px]"
              />
            ) : viewMode === 'mobile-landscape' ? (
              <MobileLandscapeUI
                onSpin={handleSpin}
                onAutoplayToggle={handleAutoplayToggle}
                onMenuToggle={() => console.log('Menu toggled')}
                onSoundToggle={() => console.log('Sound toggled')}
                onBetChange={() => console.log('Bet change requested')}
                balance={gameState.balance}
                bet={gameState.bet}
                win={gameState.win}
                className="pointer-events-auto"
                customButtons={undefined}
              />
            ) : (
              <MobilePortraitUI
                onSpin={handleSpin}
                onAutoplayToggle={handleAutoplayToggle}
                onMenuToggle={() => console.log('Menu toggled')}
                onSoundToggle={() => console.log('Sound toggled')}
                onBetChange={() => console.log('Bet change requested')}
                balance={gameState.balance}
                bet={gameState.bet}
                win={gameState.win}
                className="pointer-events-auto"
                customButtons={undefined}
              />
            )}
          </div>
        </div>
      </div>

      {/* Game Stats (Optional) */}
      {gameState.gameHistory.length > 0 && (
        <div className="bg-gray-800 px-4 py-2 text-xs text-gray-400 rounded-b-lg">
          <div className="flex justify-between">
            <span>Spins: {gameState.gameHistory.length}</span>
            <span>Total Won: ${gameState.totalWin.toFixed(2)}</span>
            <span>RTP: {((gameState.totalWin / (gameState.gameHistory.reduce((sum, spin) => sum + spin.bet, 0))) * 100).toFixed(1)}%</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default StandaloneGamePlayer;
