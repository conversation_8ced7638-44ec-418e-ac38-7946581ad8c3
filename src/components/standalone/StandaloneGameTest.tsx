import React, { useState, useCallback } from 'react';
import { useGameStore } from '../../store';
import StandaloneGamePlayer from './StandaloneGamePlayer';
import { downloadGameAsHTML } from '../../utils/gameExporter';
import { Monitor, Smartphone, Tablet, Play, Settings, Download, Share2 } from 'lucide-react';

/**
 * Standalone Game Test Page
 * 
 * This page allows you to:
 * 1. Test your generated games as playable slot machines
 * 2. Switch between desktop/mobile views
 * 3. Monitor game events and statistics
 * 4. Export games for deployment
 */
const StandaloneGameTest: React.FC = () => {
  // Use specific selectors to ensure reactivity
  const config = useGameStore((state) => state.config);
  const symbols = useGameStore((state) => state.config?.theme?.generated?.symbols);

  // Force re-render when store changes
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile-portrait' | 'mobile-landscape'>('desktop');
  const [gameEvents, setGameEvents] = useState<any[]>([]);
  const [showEvents, setShowEvents] = useState(false);
  const [gameStats, setGameStats] = useState({
    totalSpins: 0,
    totalWins: 0,
    totalWinAmount: 0,
    bigWins: 0,
    bonusTriggered: 0
  });

  // Handle game events for monitoring
  const handleGameEvent = useCallback((event: string, data: any) => {
    const eventData = {
      event,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    
    setGameEvents(prev => [eventData, ...prev.slice(0, 49)]); // Keep last 50 events
    
    // Update statistics
    if (event === 'spin:complete') {
      setGameStats(prev => ({
        ...prev,
        totalSpins: prev.totalSpins + 1,
        totalWins: data.win > 0 ? prev.totalWins + 1 : prev.totalWins,
        totalWinAmount: prev.totalWinAmount + data.win,
        bigWins: data.winType === 'big-win' ? prev.bigWins + 1 : prev.bigWins,
        bonusTriggered: data.bonusTriggered ? prev.bonusTriggered + 1 : prev.bonusTriggered
      }));
    }
  }, []);

  // Helper function to get symbol URLs from both array and object formats (same as other components)
  const getSymbolUrls = (symbols: string[] | Record<string, string> | undefined): string[] => {
    if (!symbols) return [];
    if (Array.isArray(symbols)) return symbols.filter(Boolean); // Filter out empty strings
    return Object.values(symbols).filter(Boolean); // Filter out empty strings
  };

  // Subscribe to store changes to force re-renders
  React.useEffect(() => {
    const unsubscribe = useGameStore.subscribe((state) => {
      console.log('🔄 [StandaloneGameTest] Store updated, forcing re-render');
      forceUpdate();
    });

    return unsubscribe;
  }, [forceUpdate]);

  // Debug: Log the config to see what's actually stored
  React.useEffect(() => {
    console.log('🔍 [StandaloneGameTest] Debug - Full config:', config);
    console.log('🔍 [StandaloneGameTest] Debug - Theme:', config?.theme);
    console.log('🔍 [StandaloneGameTest] Debug - Generated:', config?.theme?.generated);
    console.log('🔍 [StandaloneGameTest] Debug - Symbols raw (config):', config?.theme?.generated?.symbols);
    console.log('🔍 [StandaloneGameTest] Debug - Symbols raw (direct):', symbols);

    const symbolUrls = getSymbolUrls(symbols || config?.theme?.generated?.symbols);
    console.log('🔍 [StandaloneGameTest] Debug - Processed symbol URLs:', symbolUrls);
    console.log('🔍 [StandaloneGameTest] Debug - Symbol count:', symbolUrls.length);
  }, [config, symbols]);

  // Check if game is ready to play - use both config and direct symbols selector
  const symbolUrls = getSymbolUrls(symbols || config?.theme?.generated?.symbols);
  const isGameReady = symbolUrls.length > 0;

  const gameTitle = config?.displayName || config?.gameId || 'Generated Slot Game';
  const reels = config?.reels?.layout?.reels || 5;
  const rows = config?.reels?.layout?.rows || 3;

  // Handle game export
  const handleExportHTML = useCallback(async () => {
    try {
      await downloadGameAsHTML({
        gameId: config?.gameId || `game_${Date.now()}`,
        gameConfig: config,
        title: gameTitle,
        includeAnalytics: false
      });
    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export game. Please try again.');
    }
  }, [config, gameTitle]);

  // Handle share link generation
  const handleGenerateShareLink = useCallback(() => {
    const gameData = encodeURIComponent(JSON.stringify({
      gameId: config?.gameId,
      config: config
    }));
    const shareUrl = `${window.location.origin}/play?game=${gameData}`;

    navigator.clipboard.writeText(shareUrl).then(() => {
      alert('Share link copied to clipboard!');
    }).catch(() => {
      prompt('Copy this share link:', shareUrl);
    });
  }, [config]);

  // Handle embed code generation
  const handleGenerateEmbedCode = useCallback(() => {
    const embedCode = `<iframe
  src="${window.location.origin}/play?embed=true&game=${encodeURIComponent(JSON.stringify(config))}"
  width="1200"
  height="800"
  frameborder="0"
  allowfullscreen>
</iframe>`;

    navigator.clipboard.writeText(embedCode).then(() => {
      alert('Embed code copied to clipboard!');
    }).catch(() => {
      prompt('Copy this embed code:', embedCode);
    });
  }, [config]);

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Debug Info Bar */}
      <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
        <div className="max-w-7xl mx-auto">
          <div className="text-sm text-yellow-800">
            <strong>🔍 Debug:</strong> Config: {config ? '✅' : '❌'} |
            Symbols (config): {config?.theme?.generated?.symbols ? '✅' : '❌'} |
            Symbols (direct): {symbols ? '✅' : '❌'} |
            Processed: {symbolUrls.length} |
            Ready: {isGameReady ? '✅' : '❌'}
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Standalone Game Player</h1>
              <p className="text-gray-600">Test your generated slot machine as a playable game</p>
            </div>
            
            <div className="flex items-center gap-4">
              {/* View Mode Selector */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('desktop')}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors ${
                    viewMode === 'desktop' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Monitor className="w-4 h-4" />
                  Desktop
                </button>
                <button
                  onClick={() => setViewMode('mobile-portrait')}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors ${
                    viewMode === 'mobile-portrait' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Smartphone className="w-4 h-4" />
                  Mobile
                </button>
                <button
                  onClick={() => setViewMode('mobile-landscape')}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm transition-colors ${
                    viewMode === 'mobile-landscape' 
                      ? 'bg-white text-gray-900 shadow-sm' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Tablet className="w-4 h-4" />
                  Landscape
                </button>
              </div>

              {/* Action Buttons */}
              <button
                onClick={() => setShowEvents(!showEvents)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Settings className="w-4 h-4" />
                {showEvents ? 'Hide' : 'Show'} Events
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {!isGameReady ? (
          /* Game Not Ready */
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Play className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Game Not Ready</h3>
            <p className="text-gray-600 mb-4">
              Please complete the game generation process first. You need at least symbols generated to test the game.
            </p>
            <div className="flex gap-2 mb-4">
              <button
                onClick={() => {
                  const storeState = useGameStore.getState();
                  console.log('🔍 [DEBUG] Full store state:', storeState);
                  console.log('🔍 [DEBUG] Config:', storeState.config);
                  console.log('🔍 [DEBUG] Theme:', storeState.config?.theme);
                  console.log('🔍 [DEBUG] Generated:', storeState.config?.theme?.generated);
                  console.log('🔍 [DEBUG] Symbols raw:', storeState.config?.theme?.generated?.symbols);
                  console.log('🔍 [DEBUG] Symbols type:', typeof storeState.config?.theme?.generated?.symbols);
                  console.log('🔍 [DEBUG] Is array?:', Array.isArray(storeState.config?.theme?.generated?.symbols));

                  if (storeState.config?.theme?.generated?.symbols) {
                    const symbols = storeState.config.theme.generated.symbols;
                    if (Array.isArray(symbols)) {
                      console.log('🔍 [DEBUG] Array symbols:', symbols);
                      console.log('🔍 [DEBUG] Array length:', symbols.length);
                      console.log('🔍 [DEBUG] Non-empty symbols:', symbols.filter(Boolean));
                    } else {
                      console.log('🔍 [DEBUG] Object symbols:', symbols);
                      console.log('🔍 [DEBUG] Object keys:', Object.keys(symbols));
                      console.log('🔍 [DEBUG] Object values:', Object.values(symbols));
                      console.log('🔍 [DEBUG] Non-empty values:', Object.values(symbols).filter(Boolean));
                    }
                  }

                  const processedSymbols = getSymbolUrls(storeState.config?.theme?.generated?.symbols);
                  console.log('🔍 [DEBUG] Processed symbols:', processedSymbols);
                  console.log('🔍 [DEBUG] Processed count:', processedSymbols.length);

                  alert(`Check console for details. Found ${processedSymbols.length} symbols.`);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Debug: Check Store State
              </button>

              <button
                onClick={() => {
                  const { updateConfig, config } = useGameStore.getState();
                  const testSymbols = {
                    wild: 'https://via.placeholder.com/200/FF0000/FFFFFF?text=WILD',
                    scatter: 'https://via.placeholder.com/200/00FF00/FFFFFF?text=SCATTER',
                    high1: 'https://via.placeholder.com/200/0000FF/FFFFFF?text=HIGH1',
                    medium1: 'https://via.placeholder.com/200/FFFF00/000000?text=MED1',
                    low1: 'https://via.placeholder.com/200/FF00FF/FFFFFF?text=LOW1'
                  };

                  updateConfig({
                    theme: {
                      ...config?.theme,
                      generated: {
                        ...config?.theme?.generated,
                        symbols: testSymbols,
                        background: 'https://via.placeholder.com/1920x1080/123456/FFFFFF?text=BACKGROUND',
                        frame: 'https://via.placeholder.com/800x600/654321/FFFFFF?text=FRAME'
                      }
                    }
                  });

                  console.log('✅ Test symbols added to store');
                  alert('Test symbols added! Check the game now.');
                }}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Add Test Symbols
              </button>

              <button
                onClick={() => {
                  window.location.reload();
                }}
                className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
              >
                Force Refresh
              </button>

              <button
                onClick={() => {
                  window.open('/debug-store', '_blank');
                }}
                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                Debug Store
              </button>
            </div>
            <div className="text-sm text-gray-500">
              <p>Debug Information:</p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Config exists: {config ? '✅' : '❌'}</li>
                <li>Theme exists: {config?.theme ? '✅' : '❌'}</li>
                <li>Generated exists: {config?.theme?.generated ? '✅' : '❌'}</li>
                <li>Symbols raw: {config?.theme?.generated?.symbols ? '✅' : '❌'}</li>
                <li>Symbol URLs found: {symbolUrls.length}</li>
                <li>Reels config: {config?.reels?.layout ? '✅' : '❌'}</li>
              </ul>
              <div className="mt-3 p-2 bg-gray-100 rounded text-xs">
                <p><strong>Raw symbols data:</strong></p>
                <pre className="whitespace-pre-wrap break-all">
                  {JSON.stringify(config?.theme?.generated?.symbols, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Game Player - Main Area */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                {/* Game Info */}
                <div className="bg-gray-50 px-6 py-4 border-b">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900">{gameTitle}</h2>
                      <p className="text-sm text-gray-600">
                        {reels}×{rows} Grid • {config?.reels?.payMechanism || 'betlines'} • 
                        {config?.rtp?.target || 96}% RTP
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">View Mode</div>
                      <div className="text-lg font-semibold text-gray-900 capitalize">
                        {viewMode.replace('-', ' ')}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Standalone Game Player */}
                <div className="p-6">
                  <StandaloneGamePlayer
                    viewMode={viewMode}
                    onGameEvent={handleGameEvent}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* Sidebar - Stats and Events */}
            <div className="space-y-6">
              {/* Game Statistics */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Game Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Spins</span>
                    <span className="font-semibold">{gameStats.totalSpins}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Wins</span>
                    <span className="font-semibold">{gameStats.totalWins}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Win Amount</span>
                    <span className="font-semibold">${gameStats.totalWinAmount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Big Wins</span>
                    <span className="font-semibold">{gameStats.bigWins}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Bonus Triggered</span>
                    <span className="font-semibold">{gameStats.bonusTriggered}</span>
                  </div>
                  {gameStats.totalSpins > 0 && (
                    <div className="pt-3 border-t">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Hit Rate</span>
                        <span className="font-semibold">
                          {((gameStats.totalWins / gameStats.totalSpins) * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Export Options */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Export Game</h3>
                <div className="space-y-3">
                  <button
                    onClick={handleExportHTML}
                    className="w-full flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Download className="w-4 h-4" />
                    Download HTML
                  </button>
                  <button
                    onClick={handleGenerateShareLink}
                    className="w-full flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Share2 className="w-4 h-4" />
                    Generate Share Link
                  </button>
                  <button
                    onClick={handleGenerateEmbedCode}
                    className="w-full flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                    Embed Code
                  </button>
                </div>
              </div>

              {/* Game Events Log */}
              {showEvents && (
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Game Events</h3>
                    <button
                      onClick={() => setGameEvents([])}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Clear
                    </button>
                  </div>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {gameEvents.length === 0 ? (
                      <p className="text-gray-500 text-sm">No events yet. Start playing to see events.</p>
                    ) : (
                      gameEvents.map((event, index) => (
                        <div key={index} className="text-xs bg-gray-50 rounded p-2">
                          <div className="flex justify-between items-start">
                            <span className="font-mono text-blue-600">{event.event}</span>
                            <span className="text-gray-500">{event.timestamp}</span>
                          </div>
                          {event.data && (
                            <div className="mt-1 text-gray-600">
                              {JSON.stringify(event.data, null, 2).slice(0, 100)}
                              {JSON.stringify(event.data).length > 100 && '...'}
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StandaloneGameTest;
