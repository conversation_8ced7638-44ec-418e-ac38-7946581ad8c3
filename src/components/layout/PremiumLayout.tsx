import React, { useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useGameStore } from '../../store';
import { NINTENDO_RED } from '../GameCrafterTheme';
import {
  ChevronLeft, ChevronRight, Save, Play,
  Home, Settings, User, Lightbulb, Gamepad2, Palette, Sparkles
} from 'lucide-react';
import { useSidebarStore } from '../../stores/sidebarStore';
import { useModalStore } from '../../stores/modalStore';
import BrandLogo from '../ui/BrandLogo';
import VerticalStepSidebar from '../navigation/VerticalStepSidebar';
import GridPreviewWrapper from '../visual-journey/grid-preview/GridPreviewWrapper';
import { CSSPreviewWrapper } from '../mockups';
import Step4PixiPreview from '../animation-lab/Step4PixiPreview';
import Step3PixiPreview from '../visual-journey/steps/Step3PixiPreview';
import SpriteSheetGeneratorModal from '../modals/SpriteSheetGeneratorModal';
import PixiPreviewWrapper from '../mockups/PixiPreviewWrapper';

/**
 * PremiumLayout - The consistent global UI container for the Game Crafter experience
 * 
 * This component is inspired by professional game development tools with aesthetic cues from 
 * Nintendo's design language and high-end game creation software.
 */
interface PremiumLayoutProps {
  children: React.ReactNode;
  currentStep: number;
  totalSteps: number;
  stepTitle: string;
  stepDescription: string;
  onPrevStep: () => void;
  onNextStep: () => void;
  onSave: () => void;
  onPreview: () => void;
  showCanvas?: boolean;
  hasUnsavedChanges?: boolean;
  gameId?: string;
  gameName?: string;
}

const PremiumLayout: React.FC<PremiumLayoutProps> = ({
  children,
  currentStep,
  totalSteps,
  stepTitle,
  stepDescription,
  onPrevStep,
  onNextStep,
  onSave,
  onPreview,
  showCanvas = true,
  hasUnsavedChanges = false,
  gameId = '',
  gameName = 'New Game'
}) => {
  // Use sidebar store
  const { isNavOpen, isSidebarCollapsed, toggleSidebar } = useSidebarStore();

  // Modal state management
  const {
    isSpriteSheetGeneratorOpen,
    openSpriteSheetGenerator,
    closeSpriteSheetGenerator
  } = useModalStore();

  // Log the sidebar state in this component
  // Layout state: isNavOpen, isSidebarCollapsed
  
  // Tip state
  const [showTip, setShowTip] = React.useState(false);
  const tipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Precomputed values
  const progress = totalSteps > 0 ? (currentStep / (totalSteps - 1)) * 100 : 0;
  
  // Tips for each step
  const stepTips = [
    "Choose a theme that inspires you. The color palette will influence your game's mood.",
    "Consider your target audience when selecting game mechanics.",
    "A well-balanced grid layout provides better gameplay experience.",
    "Create distinct symbols that are easy to recognize even at small sizes.",
    "Your background should complement the symbols without overwhelming them.",
    "Sound effects and music greatly enhance the immersive experience.",
    "Animation timing is crucial - not too fast, not too slow.",
    "Bonus features keep players engaged and increase retention.",
    "Balance is key - make your game challenging but not frustrating.",
    "Testing with real users will reveal aspects you might have missed.",
    "Ensure your game complies with regulations in your target markets.",
    "Double-check all APIs and integrations before final export."
  ];
  
  // Show tip on initial load with delay
  React.useEffect(() => {
    const initialDelay = setTimeout(() => {
      setShowTip(true);
      
      // Auto-hide after 8 seconds
      tipTimeoutRef.current = setTimeout(() => {
        setShowTip(false);
      }, 8000);
    }, 3000);
    
    // Cleanup
    return () => {
      clearTimeout(initialDelay);
      if (tipTimeoutRef.current) {
        clearTimeout(tipTimeoutRef.current);
      }
    };
  }, [currentStep]);
  
  // Handle tip toggle
  const toggleTip = () => {
    // Clear any existing timeout
    if (tipTimeoutRef.current) {
      clearTimeout(tipTimeoutRef.current);
    }
    
    if (!showTip) {
      // Show tip
      setShowTip(true);
      
      // Auto-hide after 8 seconds
      tipTimeoutRef.current = setTimeout(() => {
        setShowTip(false);
      }, 8000);
    } else {
      // Hide tip
      setShowTip(false);
    }
  };
  
  // Handle direct step navigation
  const handleStepClick = (stepNumber: number) => {
    if (stepNumber !== currentStep) {
      // Use the store's setStep function for reliable navigation
      useGameStore.getState().setStep(stepNumber);
    }
  };
  
  return (
    <div className={`premium-layout h-screen flex flex-col overflow-hidden bg-gray-50 ${isSidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'}`}>
      {/* Premium Header */}
      <header className="bg-white border-b border-gray-200 shadow-sm z-50">
        <div className="flex items-center justify-between px-6 h-16">
          {/* Brand Logo with Navigation Toggle */}
          <BrandLogo 
            gameName={gameName} 
            showToggle={true}
          />
          
          {/* Right Actions */}
          <div className="flex items-center space-x-3">
            {/* Game ID Display */}
            {gameId && (
              <div className="hidden md:flex items-center">
                <span className="px-3 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                  ID: {gameId}
                </span>
              </div>
            )}
            
            {/* Tip Toggle */}
            <motion.button
              onClick={toggleTip}
              className={`p-2 rounded-full ${showTip ? 'bg-amber-100 text-amber-700' : 'hover:bg-gray-100 text-gray-500'}`}
              whileTap={{ scale: 0.95 }}
              title="Show Tip"
              aria-label="Toggle design tip"
              aria-pressed={showTip}
            >
              <Lightbulb size={18} />
            </motion.button>
            
            {/* Home Dashboard */}
            <motion.button
              onClick={() => {
                // Clear game type to show dashboard within PremiumApp
                if (window.useGameStore) {
                  window.useGameStore.setState({ gameType: null });
                } else {
                  window.location.href = '/home';
                }
              }}
              className="p-2 rounded-full hover:bg-gray-100 text-gray-500"
              whileTap={{ scale: 0.95 }}
              title="Home Dashboard"
              aria-label="Go to home dashboard"
            >
              <Home size={18} />
            </motion.button>
            
            {/* Test Game */}
            <motion.button
              onClick={onPreview}
              className="p-2 rounded-full hover:bg-gray-100 text-gray-500"
              whileTap={{ scale: 0.95 }}
              title="Test Game"
              aria-label="Preview and test the game"
            >
              <Gamepad2 size={18} />
            </motion.button>
            
            {/* Settings */}
            <motion.button
              onClick={() => {/* Open settings */}}
              className="p-2 rounded-full hover:bg-gray-100 text-gray-500"
              whileTap={{ scale: 0.95 }}
              title="Settings"
              aria-label="Open settings"
            >
              <Settings size={18} />
            </motion.button>
            
            {/* User */}
            <motion.div
              className="w-8 h-8 rounded-full flex items-center justify-center cursor-pointer"
              whileTap={{ scale: 0.95 }}
              style={{ backgroundColor: NINTENDO_RED }}
              role="button"
              tabIndex={0}
              aria-label="User profile"
            >
              <User size={16} className="text-white" />
            </motion.div>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="h-1 bg-gray-100">
          <motion.div 
            className="h-full"
            style={{ backgroundColor: NINTENDO_RED }}
            initial={{ width: '0%' }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
          />
        </div>
      </header>
      
      {/* Main Content Area with Side Navigation */}
      <div className="flex-1 flex overflow-hidden">
        {/* Premium Side Navigation - Animated */}
        <AnimatePresence initial={false} mode="sync">
          {!isSidebarCollapsed && (
            <motion.div
              key="main-sidebar"
              className="w-64 bg-white border-r border-gray-200 overflow-y-auto z-40"
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 256, opacity: 1 }}
              exit={{ width: 0, opacity: 0, position: 'absolute', left: 0, pointerEvents: 'none' }}
              transition={{ 
                type: "spring",
                stiffness: 300,
                damping: 30,
                duration: 0.25
              }}
              data-sidebar-type="main"
              data-testid="main-sidebar"
            >
              <div className="p-5">
                <h2 className="text-xl font-bold mb-4" style={{ color: NINTENDO_RED }}>Game Creation</h2>
                
                {/* Steps List */}
                <div className="space-y-1 mt-6">
                  {Array.from({ length: totalSteps }).map((_, index) => {
                    const isActive = index === currentStep;
                    const isCompleted = index < currentStep;
                    
                    return (
                      <div 
                        key={`step-${index}`}
                        className={`
                          flex items-center p-3 rounded-lg cursor-pointer
                          ${isActive ? 'bg-red-50' : 'hover:bg-gray-50'}
                        `}
                        onClick={() => handleStepClick(index)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleStepClick(index);
                          }
                        }}
                        tabIndex={0}
                        role="button"
                        aria-label={`Go to step ${index + 1}: ${index === 0 ? 'Theme Selection' : 
                          index === 1 ? 'Game Type' :
                          index === 2 ? 'Grid Layout' :
                          index === 3 ? 'Symbols' :
                          index === 4 ? 'Game Assets' :
                          index === 5 ? 'Animation Studio' : 
                          index === 6 ? 'Win Animations' :
                          index === 7 ? 'Loading Experience' :
                          index === 8 ? 'Game Splash & Branding' :
                          index === 9 ? 'Audio & Experience' :
                          index === 10 ? 'Bonus Features' :
                          index === 11 ? 'Math Model' :
                          index === 12 ? 'Simulation' :
                          index === 13 ? 'Compliance' : 
                          index === 14 ? 'API Export' : `Step ${index + 1}`}`}
                        aria-current={isActive ? 'step' : undefined}
                      >
                        <div 
                          className={`
                            w-6 h-6 rounded-full flex items-center justify-center mr-3
                            ${isActive 
                              ? 'bg-red-500 text-white' 
                              : isCompleted 
                                ? 'bg-green-500 text-white' 
                                : 'bg-gray-200 text-gray-600'}
                          `}
                          style={{ backgroundColor: isActive ? NINTENDO_RED : isCompleted ? '#10B981' : '' }}
                        >
                          {isCompleted ? (
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M2.5 6L5 8.5L9.5 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          ) : (
                            <span className="text-xs">{index + 1}</span>
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <div className={`text-sm font-medium ${isActive ? 'text-red-700' : 'text-gray-900'}`}>
                            {index === 0 ? 'Theme Selection' : 
                             index === 1 ? 'Game Type' :
                             index === 2 ? 'Grid Layout' :
                             index === 3 ? 'Game Assets' : 
                             index === 4 ? 'Symbol Creation' :
                             index === 5 ? 'Symbol Animation' :
                             index === 6 ? 'Animation & Masking Studio' :
                             index === 7 ? 'Win Animations' :
                             index === 8 ? 'Loading Experience' :
                             index === 9 ? 'Game Splash & Branding' :
                             index === 10 ? 'Audio & Experience' :
                             index === 11 ? 'Bonus Features' :
                             index === 12 ? 'Math Model' :
                             index === 13 ? 'Game Simulation' :
                             index === 14 ? 'Market Compliance' : 
                             index === 15 ? 'API Export' : `Step ${index + 1}`}
                          </div>
                          
                          {isActive && (
                            <div className="text-xs text-gray-500 mt-1">{stepDescription}</div>
                          )}
                        </div>
                        
                        {isActive && (
                          <div 
                            className="w-1.5 h-10 rounded-full"
                            style={{ backgroundColor: NINTENDO_RED }}
                            aria-hidden="true"
                          />
                        )}
                      </div>
                    );
                  })}
                </div>

                {/* Animation Symbol Generator Button */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <button
                    onClick={openSpriteSheetGenerator}
                    className="w-full flex items-center p-3 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 border border-blue-200 transition-all duration-200 group"
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                      <Sparkles className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1 text-left">
                      <div className="text-sm font-medium text-gray-800">Animation Symbol</div>
                      <div className="text-xs text-gray-600">Generate AI sprites</div>
                    </div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full opacity-60 group-hover:opacity-100 transition-opacity"></div>
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Vertical Step Sidebar - Only visible when main sidebar is collapsed */}
        <AnimatePresence initial={false} mode="sync">
          {isSidebarCollapsed && (
            <div key="vertical-sidebar-container" className="relative z-30">
              <VerticalStepSidebar 
                key="vertical-sidebar"
                currentStep={currentStep}
                totalSteps={totalSteps}
                onStepClick={handleStepClick}
              />
            </div>
          )}
        </AnimatePresence>
        
        {/* Main Content with Header and Workspace */}
        <div 
          className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out
            ${isSidebarCollapsed ? 'ml-20 sidebar-content-collapsed' : 'sidebar-content-expanded'}`}
          data-content-shifted={isSidebarCollapsed ? 'true' : 'false'}
          data-sidebar-state={isSidebarCollapsed ? 'closed' : 'open'}
          data-testid="sidebar-content-container"
        >
          {/* Step Header with Title and Navigation Controls */}
          <div className="bg-white border-b border-gray-200 p-4 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold">{stepTitle}</h1>
              <p className="text-gray-500">{stepDescription}</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="bg-gray-100 px-3 py-1 rounded-full text-sm">
                Step {currentStep + 1} of {totalSteps}
              </div>
            </div>
          </div>
          
          {/* Workspace Split View */}
          <div className="flex-1 flex overflow-hidden" style={{ display: 'flex', flexDirection: 'row', width: '100%' }}>
            {/* Main Content Area - Flexible Width */}
            <div 
              className={`${showCanvas ? 'border-r border-gray-200' : ''} overflow-y-auto p-3`}
              style={{ 
                width: showCanvas ? '50%' : '100%', 
                flex: showCanvas ? '0 0 50%' : '1 1 100%'
              }}
            >
              {children}
            </div>
            
            {/* Canvas Area - Shown conditionally */}
            {showCanvas && (
              <div 
                className="bg-gray-100 flex flex-col relative" 
                id={`right-panel-step-${currentStep}`}
                data-testid={`right-panel-step-${currentStep}`}
                data-step-number={currentStep}
                style={{ 
                  width: '50%', 
                  flex: '0 0 50%',
                  height: '100%',
                  overflow: 'hidden',
                  position: 'relative'
                }}
              >
                {/* Step 3 (index 2) gets CSS mockup, Step 4 (index 3) gets PixiJS preview, Step 5 (index 4) handles its own internal PIXI preview, Step 6 (index 5) gets PixiJS preview, Step 7 (index 6) gets PIXI GridPreviewWrapper, others get Game Canvas */}
                {currentStep === 2 || currentStep === 3 ? (
                  <PixiPreviewWrapper className="h-full" stepSource={`step${currentStep + 1}`} />
                ) : currentStep === 3 ? (
                  <Step4PixiPreview className="h-full" stepSource={`step${currentStep + 1}`} />
                ) : currentStep === 4 ? (
                  <PixiPreviewWrapper className="h-full" stepSource={`step${currentStep + 1}`} />
                ) : currentStep === 5 ? (
                  <PixiPreviewWrapper className="h-full" stepSource={`step${currentStep + 1}`} />
                ) : currentStep === 7 ? (
                  <GridPreviewWrapper className="h-full" />
                ) : currentStep === 6 || currentStep === 11 ? (
                      <PixiPreviewWrapper className="h-full" stepSource={`step${currentStep + 1}`} />
                ) : (
                  <>
                    <div className="p-3 bg-gray-200 border-b border-gray-300 flex justify-between items-center">
                      <div className="text-sm font-medium flex items-center">
                        <Palette size={16} className="mr-1.5" style={{ color: NINTENDO_RED }} />
                        <span>Premium Slot Preview</span>
                      </div>
                      
                      <div className="flex space-x-2">
                        <button 
                          className="p-1 rounded hover:bg-gray-300 text-gray-700"
                          aria-label="Zoom in"
                        >
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.5 13.5V9.5H2.5V6.5H6.5V2.5H9.5V6.5H13.5V9.5H9.5V13.5H6.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                        <button 
                          className="p-1 rounded hover:bg-gray-300 text-gray-700"
                          aria-label="Toggle grid view"
                        >
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.5 6.5H2.5V2.5H6.5V6.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M13.5 6.5H9.5V2.5H13.5V6.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M6.5 13.5H2.5V9.5H6.5V13.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M13.5 13.5H9.5V9.5H13.5V13.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                    
                    {/* Premium Slot Preview Placeholder */}
                    <div className="flex-1 overflow-hidden p-4 flex items-center justify-center bg-gray-800">
                      <div className="text-center">
                        <p className="text-gray-300 mb-3">Premium Slot Preview</p>
                        <p className="text-xs text-gray-500">Preview is available from symbol generation onward.</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
          
          {/* Bottom Navigation Controls */}
          <div className="bg-white border-t border-gray-200 p-4 flex justify-between items-center">
            {/* Left side - Back button */}
            <div>
              <button
                onClick={onPrevStep}
                disabled={currentStep === 0}
                className={`
                  flex items-center px-4 py-2 rounded-lg
                  ${currentStep === 0 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'}
                `}
                aria-label="Go to previous step"
              >
                <ChevronLeft size={18} className="mr-1" />
                <span>Previous</span>
              </button>
            </div>
            
            {/* Center - Actions */}
            <div className="flex space-x-3">
              {/* Save Button - Animated when has unsaved changes */}
              <motion.button
                onClick={onSave}
                className={`
                  px-4 py-2 rounded-lg flex items-center
                  ${hasUnsavedChanges 
                    ? 'bg-blue-50 border border-blue-200 text-blue-700' 
                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'}
                `}
                animate={hasUnsavedChanges ? {
                  scale: [1, 1.03, 1],
                  boxShadow: [
                    '0 0 0 rgba(59, 130, 246, 0)',
                    '0 0 8px rgba(59, 130, 246, 0.5)',
                    '0 0 0 rgba(59, 130, 246, 0)'
                  ]
                } : {}}
                transition={{ 
                  repeat: hasUnsavedChanges ? Infinity : 0, 
                  repeatDelay: 3,
                  duration: 1
                }}
                aria-label="Save progress"
              >
                <Save size={18} className={`mr-1.5 ${hasUnsavedChanges ? 'text-blue-700' : 'text-gray-500'}`} />
                <span>Save Progress</span>
                {hasUnsavedChanges && (
                  <div className="ml-2 w-2 h-2 rounded-full bg-blue-600 animate-pulse" aria-hidden="true" />
                )}
              </motion.button>
              
              {/* Test Game Button - Only visible from step 7 onwards */}
              {currentStep >= 6 && (
                <div className=''>
                <button
                  onClick={onPreview}
                  className="px-4 py-2 rounded-lg flex items-center bg-green-600 hover:bg-green-700 text-white"
                  aria-label="Test the game"
                  data-testid="test-game-button"
                >
                  <Play size={18} className="mr-1.5" />
                  <span>Test Game</span>
                </button>
                <button
                            onClick={() => {
                              // Get current gameStore state
                              const gameStore = useGameStore.getState();
                              const gameData = {
                                gameId: gameStore.config?.gameId || `game_${Date.now()}`,
                                config: gameStore.config,
                                currentStep: gameStore.currentStep,
                                answers: gameStore.answers
                              };

                              // Encode game data for URL
                              const encodedData = encodeURIComponent(JSON.stringify(gameData));
                              const standaloneUrl = `/play?game=${encodedData}`;

                              console.log('🚀 Opening standalone with game data:', gameData);
                              window.open(standaloneUrl, '_blank');
                            }}
                            className="px-4 py-2 bg-purple-600 text-white rounded-lg flex items-center gap-2 hover:bg-purple-700"
                          >
                            <Play className="w-4 h-4" />
                            Standalone
                          </button>
                </div>
              )}
            </div>
            
            {/* Right side - Next button */}
            <div className="relative">
              <button
                id="premium-next-step-button"
                onClick={onNextStep}
                tabIndex={0}
                disabled={currentStep === totalSteps - 1}
                className={`
                  next-step-button flex items-center py-2.5 px-5 rounded-md transition
                  ${currentStep === totalSteps - 1 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-red-600 hover:bg-red-700 text-white font-semibold cursor-pointer shadow-sm hover:shadow-md'}
                  transition-all duration-200 ease-in-out
                `}
                style={{ 
                  backgroundColor: currentStep === totalSteps - 1 ? undefined : NINTENDO_RED,
                }}
                aria-label="Go to next step"
              >
                <span className="font-medium">{currentStep === totalSteps - 1 ? 'Complete' : 'Next Step'}</span>
                <ChevronRight size={18} className="ml-1.5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Sprite Sheet Generator Modal */}
      <SpriteSheetGeneratorModal
        isOpen={isSpriteSheetGeneratorOpen}
        onClose={closeSpriteSheetGenerator}
      />
    </div>
  );
};

export default PremiumLayout;