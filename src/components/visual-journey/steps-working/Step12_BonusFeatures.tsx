import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useGameStore } from '../../../store';
import { Gift, Coins, Zap, AlertCircle, Plus, Minus, RotateCw, RefreshCw, Loader, <PERSON>rkles } from 'lucide-react';
import { But<PERSON> } from '../../Button';
import BonusSymbolCarousel from '../../Step12_BonusFeatures/Symbol_carousel';
import { SymbolConfig } from '../../../types/EnhancedAnimationLabStep4';
import { enhancedOpenaiClient } from '../../../utils/enhancedOpenaiClient';
import { saveImage } from '../../../utils/imageSaver';


// Bonus Symbol Preset Configuration
const BONUS_SYMBOL_PRESETS = [
  {
    type: 'bonus' as const,
    name: 'Bonus',
    description: 'Bonus game trigger symbol',
    importance: 5,
    rarity: 'epic' as const,
    weight: 2,
    enabled: true,
    defaultPrompt: 'treasure chest'
  },
  {
    type: 'free' as const,
    name: 'Free Spins',
    description: 'Free spins trigger symbol',
    importance: 5,
    rarity: 'epic' as const,
    weight: 2,
    enabled: true,
    defaultPrompt: 'spinning wheel'
  },
  {
    type: 'jackpot' as const,
    name: 'Jackpot',
    description: 'Jackpot trigger symbol',
    importance: 5,
    rarity: 'legendary' as const,
    weight: 1,
    enabled: false, // Disabled for now
    defaultPrompt: 'diamond crown'
  }
];

export const BonusFeatures: React.FC = () => {
  const { config, updateConfig } = useGameStore();
  const { bonus } = config;
  const gameId = config?.gameId || config?.displayName || 'default';

  // Track expanded features
  const [expandedFeatures, setExpandedFeatures] = useState<string[]>(['freeSpins', 'jackpots']);
  const [mathModel, setMathModel] = useState({
    featureRTP: 0,
    hitFrequency: 0,
    maxWin: 0
  });

  // Bonus Symbol Generation State
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedBonusSymbolType, setSelectedBonusSymbolType] = useState<'bonus' | 'free' | 'jackpot'>('bonus');
  const [bonusSymbols, setBonusSymbols] = useState<SymbolConfig[]>([]);
  const [selectedSymbolId, setSelectedSymbolId] = useState<string | null>(null);

  // Symbol Configuration State (like Step5)
  const [contentType, setContentType] = useState<'symbol-bonus' | 'symbol-free' | 'text-only'>('symbol-bonus');
  const [size, setSize] = useState<'1x1' | '1x3' | '2x2' | '3x3' | '4x4'>('1x1');
  const [animationComplexity, setAnimationComplexity] = useState<'simple' | 'medium' | 'complex'>('simple');

  // Layout template state (like Step5)
  const [selectedLayoutTemplate, setSelectedLayoutTemplate] = useState<'text-top' | 'text-bottom' | 'text-overlay'>('text-bottom');

  // Track preview states separately for each feature
  const [previewStates, setPreviewStates] = useState({
    wheel: false,
    pickAndClick: false,
    holdAndSpin: false
  });

  // Reference for setting which preview is actively displayed (for UI purposes)
  const [activePreview, setActivePreview] = useState<string | null>(null);

  // Canvas references
  const wheelCanvasRef = useRef<HTMLCanvasElement>(null);
  const holdSpinCanvasRef = useRef<HTMLCanvasElement>(null);

  // Initialize bonus symbols on component mount
  useEffect(() => {
    initializeBonusSymbols();
  }, []);

  // Helper function to get storage key for bonus symbols
  const getBonusSymbolStorageKey = useCallback((symbolType: 'bonus' | 'free' | 'jackpot'): string => {
    return `bonus_${symbolType}`;
  }, []);

  // Initialize bonus symbols from GameStore or create empty slots
  const initializeBonusSymbols = useCallback(() => {
    console.log('🎯 Initializing bonus symbols...');

    const existingBonusSymbols = config?.theme?.generated?.bonusSymbols || {};
    const initializedSymbols: SymbolConfig[] = [];

    BONUS_SYMBOL_PRESETS.forEach((preset) => {
      const storageKey = getBonusSymbolStorageKey(preset.type);
      const existingImageUrl = existingBonusSymbols[storageKey];

      const bonusSymbol: SymbolConfig = {
        id: `bonus_${preset.type}`,
        name: preset.name,
        symbolType: 'block',
        contentType: `symbol-${preset.type}` as any,
        bonusContentType: `symbol-${preset.type}` as any,
        size: '1x1',
        prompt: preset.defaultPrompt,
        animationComplexity: 'complex',
        gameSymbolType: preset.type,
        importance: preset.importance,
        rarity: preset.rarity,
        defaultDescription: preset.description,
        imageUrl: existingImageUrl || undefined,
        retryCount: 0
      };

      initializedSymbols.push(bonusSymbol);
    });

    setBonusSymbols(initializedSymbols);

    // Set first enabled symbol as selected (bonus and free, not jackpot)
    const enabledSymbols = initializedSymbols.filter(s =>
      BONUS_SYMBOL_PRESETS.find(p => p.type === s.gameSymbolType)?.enabled
    );
    if (enabledSymbols.length > 0) {
      setSelectedSymbolId(enabledSymbols[0].id);
      setPrompt(enabledSymbols[0].prompt);
      setContentType(`symbol-${enabledSymbols[0].gameSymbolType}` as any);
    }

    console.log('✅ Bonus symbols initialized:', initializedSymbols.length);
  }, [config?.theme?.generated?.bonusSymbols, getBonusSymbolStorageKey]);

  // Check if at least one bonus symbol is generated
  const hasGeneratedBonusSymbols = useCallback(() => {
    const existingBonusSymbols = config?.theme?.generated?.bonusSymbols || {};
    return Object.values(existingBonusSymbols).some(url => url && url.length > 0);
  }, [config?.theme?.generated?.bonusSymbols]);

  // Check if bonus symbols should be used (only if bonus features are enabled)
  const shouldUseBonusSymbols = useCallback(() => {
    return isFeatureEnabled('freeSpins') || isFeatureEnabled('pickAndClick') ||
      isFeatureEnabled('wheel') || isFeatureEnabled('holdAndSpin') || isFeatureEnabled('jackpots');
  }, []);

  // Enhanced prompt generation like Step5
  const generateEnhancedPrompt = useCallback((basePrompt: string, symbolContentType: string) => {
    let enhancedPrompt = 'Professional slot machine symbol design: ';

    // Add base prompt (just the symbol type like "treasure chest")
    enhancedPrompt += `${basePrompt.trim()}, `;

    // Add style and quality specifications like Step5
    enhancedPrompt += 'high-quality digital art, vibrant colors, clean design, ';
    enhancedPrompt += 'professional game asset, transparent background, ';
    enhancedPrompt += 'detailed shading and highlights, premium casino style, ';

    // Add content-specific instructions with layout template
    if (symbolContentType === 'symbol-bonus') {
      enhancedPrompt += 'with BONUS text integrated into design, golden effects, ';

      // Add layout-specific instructions
      if (selectedLayoutTemplate === 'text-top') {
        enhancedPrompt += 'LAYOUT: 5 letters B-O-N-U-S arranged horizontally at top + main symbol below, ';
      } else if (selectedLayoutTemplate === 'text-overlay') {
        enhancedPrompt += 'LAYOUT: Main symbol as background + 5 letters B-O-N-U-S overlaid on center, ';
      } else { // text-bottom (default)
        enhancedPrompt += 'LAYOUT: Main symbol in center + 5 letters B-O-N-U-S arranged horizontally below, ';
      }
      enhancedPrompt += 'SPACING: Large gaps between each element for easy separation, ';

    } else if (symbolContentType === 'symbol-free') {
      enhancedPrompt += 'with FREE text integrated into design, magical sparkles, ';

      // Add layout-specific instructions
      if (selectedLayoutTemplate === 'text-top') {
        enhancedPrompt += 'LAYOUT: 4 letters F-R-E-E arranged horizontally at top + main symbol below, ';
      } else if (selectedLayoutTemplate === 'text-overlay') {
        enhancedPrompt += 'LAYOUT: Main symbol as background + 4 letters F-R-E-E overlaid on center, ';
      } else { // text-bottom (default)
        enhancedPrompt += 'LAYOUT: Main symbol in center + 4 letters F-R-E-E arranged horizontally below, ';
      }
      enhancedPrompt += 'SPACING: Large gaps between each element for easy separation, ';

    } else if (symbolContentType === 'text-only') {
      const textWord = symbolContentType.includes('bonus') ? 'BONUS' : 'FREE';
      enhancedPrompt += `large ${textWord} text only, bold letters, premium typography, `;
    }

    enhancedPrompt += 'isolated elements that can be easily cut out, no overlapping parts.';

    return enhancedPrompt;
  }, [selectedLayoutTemplate]);

  // Get current selected bonus symbol
  const selectedSymbol = bonusSymbols.find(symbol => symbol.id === selectedSymbolId) || bonusSymbols[0];

  // Save bonus symbols to GameStore
  const saveBonusSymbolsToGameStore = useCallback((symbolsToSave: SymbolConfig[]) => {
    console.log('💾 Saving bonus symbols to GameStore:', symbolsToSave.length);

    const bonusSymbolsByKey: Record<string, string> = {};

    symbolsToSave.forEach(symbol => {
      if (symbol.gameSymbolType && (symbol.gameSymbolType === 'bonus' || symbol.gameSymbolType === 'free' || symbol.gameSymbolType === 'jackpot')) {
        const storageKey = getBonusSymbolStorageKey(symbol.gameSymbolType);
        if (symbol.imageUrl) {
          bonusSymbolsByKey[storageKey] = symbol.imageUrl;
          console.log(`💾 Saved ${symbol.gameSymbolType} -> ${storageKey}: HAS_IMAGE`);
        }
      }
    });

    // Update GameStore with bonus symbols
    updateConfig({
      theme: {
        ...config?.theme,
        generated: {
          background: config?.theme?.generated?.background || null,
          symbols: config?.theme?.generated?.symbols || {},
          frame: config?.theme?.generated?.frame || null,
          bonusSymbols: {
            ...config?.theme?.generated?.bonusSymbols,
            ...bonusSymbolsByKey
          }
        }
      }
    });

    console.log('✅ Bonus symbols saved to GameStore:', Object.keys(bonusSymbolsByKey));
  }, [config, updateConfig, getBonusSymbolStorageKey]);

  // Generate bonus symbol using OpenAI
  const handleGenerateSymbol = useCallback(async () => {
    if (!selectedSymbol || !prompt.trim()) {
      console.warn('⚠️ No symbol selected or prompt is empty');
      return;
    }

    console.log('🎨 Starting bonus symbol generation for:', selectedSymbol.name);
    setIsGenerating(true);

    try {
      // Generate enhanced prompt like Step5
      const enhancedPrompt = generateEnhancedPrompt(prompt.trim(), contentType);
      console.log('🎨 Enhanced prompt:', enhancedPrompt);

      // Generate the symbol image using the correct API format
      const result = await enhancedOpenaiClient.generateImageWithConfig({
        prompt: enhancedPrompt,
        targetSymbolId: selectedSymbol.id,
        gameId: gameId,
        count: 1
      });

      if (result.success && result.images && result.images.length > 0) {
        const imageUrl = result.images[0];
        console.log('✅ Bonus symbol generated successfully');

        // Save the image
        const savedImageResult = await saveImage(
          imageUrl,
          `bonus_${selectedSymbol.gameSymbolType}`,
          selectedSymbol.id,
          gameId
        );

        // Update the symbol with the new image
        const updatedSymbols = bonusSymbols.map(symbol =>
          symbol.id === selectedSymbol.id
            ? { ...symbol, imageUrl: savedImageResult.filePath, retryCount: 0 }
            : symbol
        );

        setBonusSymbols(updatedSymbols);

        // Save to GameStore
        saveBonusSymbolsToGameStore(updatedSymbols);

        // Dispatch bonus symbols changed event for PixiPreviewWrapper
        window.dispatchEvent(new CustomEvent('bonusSymbolsChanged', {
          detail: {
            symbols: updatedSymbols.filter(s => s.imageUrl).map(s => s.imageUrl),
            gameId: gameId,
            source: 'bonus-features',
            timestamp: Date.now()
          }
        }));

        // Show success notification
        if (typeof window !== 'undefined' && (window as any).showToast) {
          (window as any).showToast(`${selectedSymbol.name} symbol generated successfully!`, 'success');
        }

        console.log('✅ Bonus symbol generation completed');
      } else {
        throw new Error(result.error || 'No image returned from generation');
      }
    } catch (error) {
      console.error('❌ Error generating bonus symbol:', error);

      // Update retry count
      const updatedSymbols = bonusSymbols.map(symbol =>
        symbol.id === selectedSymbol.id
          ? { ...symbol, retryCount: (symbol.retryCount || 0) + 1 }
          : symbol
      );
      setBonusSymbols(updatedSymbols);

      // Show error notification
      if (typeof window !== 'undefined' && (window as any).showToast) {
        (window as any).showToast(`Failed to generate ${selectedSymbol.name} symbol. Please try again.`, 'error');
      }
    } finally {
      setIsGenerating(false);
    }
  }, [selectedSymbol, prompt, bonusSymbols, saveBonusSymbolsToGameStore]);

  // Handle symbol selection
  const handleSymbolSelection = useCallback((symbolId: string) => {
    setSelectedSymbolId(symbolId);
    const symbol = bonusSymbols.find(s => s.id === symbolId);
    if (symbol) {
      setPrompt(symbol.prompt);
      setSelectedBonusSymbolType(symbol.gameSymbolType as 'bonus' | 'free' | 'jackpot');
    }
  }, [bonusSymbols]);

  // Handle prompt change
  const handlePromptChange = useCallback((newPrompt: string) => {
    setPrompt(newPrompt);

    // Update the selected symbol's prompt
    if (selectedSymbol) {
      const updatedSymbols = bonusSymbols.map(symbol =>
        symbol.id === selectedSymbol.id
          ? { ...symbol, prompt: newPrompt }
          : symbol
      );
      setBonusSymbols(updatedSymbols);
    }
  }, [selectedSymbol, bonusSymbols]);

  // Function to draw wheel bonus preview
  const drawWheel = (segmentCount: number, hasLevelUp: boolean, hasRespin: boolean) => {
    const canvas = wheelCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.45;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw outer ring
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius + 5, 0, Math.PI * 2);
    ctx.fillStyle = '#F8C630';
    ctx.fill();

    // Define segment colors
    const colors = [
      '#EF5350', '#42A5F5', '#66BB6A', '#FFA726',
      '#8D6E63', '#26A69A', '#EC407A', '#7E57C2',
      '#5C6BC0', '#FFB74D', '#9CCC65', '#4DD0E1'
    ];

    // Define segment types - prizes, special segments, etc.
    let segments: { type: string, value: number }[] = [];
    const maxPrize = bonus?.wheel?.maxMultiplier || 50;

    // Create an array of random prizes
    for (let i = 0; i < segmentCount; i++) {
      // Add some level up and respin segments if enabled
      if (hasLevelUp && i === 2) {
        segments.push({ type: 'levelup', value: 0 });
      } else if (hasRespin && i === 5) {
        segments.push({ type: 'respin', value: 0 });
      } else {
        // Generate random prize values distributed based on segment position
        let value;
        if (i < segmentCount * 0.6) { // 60% low values
          value = Math.floor(Math.random() * (maxPrize * 0.2) + 1);
        } else if (i < segmentCount * 0.9) { // 30% medium values
          value = Math.floor(Math.random() * (maxPrize * 0.5) + (maxPrize * 0.2));
        } else { // 10% high values
          value = Math.floor(Math.random() * (maxPrize * 0.3) + (maxPrize * 0.7));
        }
        segments.push({ type: 'prize', value });
      }
    }

    // Shuffle segments
    segments = segments.sort(() => Math.random() - 0.5);

    // Draw wheel segments
    const anglePerSegment = (Math.PI * 2) / segmentCount;
    for (let i = 0; i < segmentCount; i++) {
      const startAngle = i * anglePerSegment;
      const endAngle = (i + 1) * anglePerSegment;

      // Draw segment
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();

      // Coloring based on segment type
      if (segments[i].type === 'levelup') {
        ctx.fillStyle = '#FFD700'; // Gold for level up
      } else if (segments[i].type === 'respin') {
        ctx.fillStyle = '#D1C4E9'; // Light purple for respin
      } else {
        ctx.fillStyle = colors[i % colors.length];
      }
      ctx.fill();

      // Add stroke
      ctx.lineWidth = 1;
      ctx.strokeStyle = '#FFFFFF';
      ctx.stroke();

      // Draw segment text
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate(startAngle + anglePerSegment / 2);

      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.font = 'bold 14px Arial';
      ctx.fillStyle = '#FFFFFF';

      // Different text based on segment type
      if (segments[i].type === 'levelup') {
        ctx.fillText('LEVEL UP', radius * 0.7, 0);
      } else if (segments[i].type === 'respin') {
        ctx.fillText('RESPIN', radius * 0.7, 0);
      } else {
        ctx.fillText(`${segments[i].value}x`, radius * 0.7, 0);
      }

      ctx.restore();
    }

    // Draw inner circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.25, 0, Math.PI * 2);
    ctx.fillStyle = '#F8C630';
    ctx.fill();
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#FFFFFF';
    ctx.stroke();

    // Draw pointer
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - radius - 20);
    ctx.lineTo(centerX - 15, centerY - radius + 5);
    ctx.lineTo(centerX + 15, centerY - radius + 5);
    ctx.closePath();
    ctx.fillStyle = '#E53935';
    ctx.fill();
    ctx.lineWidth = 2;
    ctx.strokeStyle = '#FFFFFF';
    ctx.stroke();
  };

  // Helper function to ensure proper bonus config defaults
  const ensureBonusDefaults = (featureType: string, updates: any) => {
    const currentBonus = config.bonus || {};

    switch (featureType) {
      case 'freeSpins':
        return {
          enabled: currentBonus.freeSpins?.enabled ?? true,
          count: currentBonus.freeSpins?.count ?? 10,
          triggers: currentBonus.freeSpins?.triggers ?? [3],
          multipliers: currentBonus.freeSpins?.multipliers ?? [1],
          retriggers: currentBonus.freeSpins?.retriggers ?? true,
          ...updates
        };
      case 'pickAndClick':
        return {
          enabled: currentBonus.pickAndClick?.enabled ?? true,
          gridSize: (currentBonus.pickAndClick?.gridSize ?? [3, 3]) as [number, number],
          picks: currentBonus.pickAndClick?.picks ?? 3,
          maxPrize: currentBonus.pickAndClick?.maxPrize ?? 100,
          extraPicks: currentBonus.pickAndClick?.extraPicks ?? false,
          multipliers: currentBonus.pickAndClick?.multipliers ?? false,
          ...updates
        };
      case 'wheel':
        return {
          enabled: currentBonus.wheel?.enabled ?? true,
          segments: currentBonus.wheel?.segments ?? 8,
          maxMultiplier: currentBonus.wheel?.maxMultiplier ?? 50,
          levelUp: currentBonus.wheel?.levelUp ?? false,
          respin: currentBonus.wheel?.respin ?? false,
          ...updates
        };
      case 'holdAndSpin':
        return {
          enabled: currentBonus.holdAndSpin?.enabled ?? true,
          gridSize: (currentBonus.holdAndSpin?.gridSize ?? [3, 3]) as [number, number],
          initialRespins: currentBonus.holdAndSpin?.initialRespins ?? 3,
          maxSymbolValue: currentBonus.holdAndSpin?.maxSymbolValue ?? 100,
          resetRespins: currentBonus.holdAndSpin?.resetRespins ?? false,
          collectAll: currentBonus.holdAndSpin?.collectAll ?? false,
          ...updates
        };
      case 'jackpots':
        return {
          enabled: currentBonus.jackpots?.enabled ?? true,
          type: currentBonus.jackpots?.type ?? 'fixed',
          levels: currentBonus.jackpots?.levels ?? ['Mini', 'Minor', 'Major', 'Grand'],
          trigger: currentBonus.jackpots?.trigger ?? 'random',
          values: currentBonus.jackpots?.values ?? { Mini: 10, Minor: 50, Major: 250, Grand: 1000 },
          ...updates
        };
      default:
        return updates;
    }
  };

  // Function to render Pick & Click grid preview
  const renderPickAndClickGrid = () => {
    const gridSize = bonus?.pickAndClick?.gridSize || [3, 3];
    const picks = bonus?.pickAndClick?.picks || 3;
    const maxPrize = bonus?.pickAndClick?.maxPrize || 100;
    const hasMultipliers = !!bonus?.pickAndClick?.multipliers;
    const hasExtraPicks = !!bonus?.pickAndClick?.extraPicks;

    const rows = gridSize[0];
    const cols = gridSize[1];

    // Create grid with different symbol types
    const grid = Array(rows).fill(0).map(() => Array(cols).fill(null));

    // Randomly distribute different cell types
    let remainingPrizes = [];

    // Generate prize values with distribution
    for (let i = 0; i < rows * cols; i++) {
      let value;
      if (i < (rows * cols) * 0.5) { // 50% low prizes
        value = Math.floor(Math.random() * (maxPrize * 0.3) + 1);
      } else if (i < (rows * cols) * 0.8) { // 30% medium prizes
        value = Math.floor(Math.random() * (maxPrize * 0.4) + (maxPrize * 0.3));
      } else { // 20% high prizes
        value = Math.floor(Math.random() * (maxPrize * 0.3) + (maxPrize * 0.7));
      }
      remainingPrizes.push({ type: 'prize', value });
    }

    // Add special symbols if enabled
    if (hasExtraPicks) {
      const extraPickIndex = Math.floor(Math.random() * remainingPrizes.length);
      remainingPrizes[extraPickIndex] = { type: 'extraPick', value: 0 };
    }

    if (hasMultipliers) {
      const multiplierIndex = Math.floor(Math.random() * remainingPrizes.length);
      if (multiplierIndex !== remainingPrizes.findIndex(p => p.type === 'extraPick')) {
        remainingPrizes[multiplierIndex] = { type: 'multiplier', value: [2, 3, 5][Math.floor(Math.random() * 3)] };
      }
    }

    // Shuffle prizes and assign to grid
    remainingPrizes = remainingPrizes.sort(() => Math.random() - 0.5);

    // Fill grid with prizes
    for (let r = 0; r < rows; r++) {
      for (let c = 0; c < cols; c++) {
        if (remainingPrizes.length > 0) {
          grid[r][c] = remainingPrizes.pop();
        }
      }
    }

    // Simulate already opened cells
    const revealedCells = Array(rows).fill(0).map(() => Array(cols).fill(false));
    const openedCount = Math.min(picks, 4);

    // Randomly open a few cells
    for (let i = 0; i < openedCount; i++) {
      let r, c;
      do {
        r = Math.floor(Math.random() * rows);
        c = Math.floor(Math.random() * cols);
      } while (revealedCells[r][c]);

      revealedCells[r][c] = true;
    }

    return { grid, revealedCells, picks, totalPicks: picks };
  };

  const isFeatureEnabled = (feature: string) => {
    switch (feature) {
      case 'freeSpins':
        return bonus?.freeSpins?.enabled || false;
      case 'pickAndClick':
        return bonus?.pickAndClick?.enabled || false;
      case 'wheel':
        return bonus?.wheel?.enabled || false;
      case 'holdAndSpin':
        return bonus?.holdAndSpin?.enabled || false;
      case 'jackpots':
        return bonus?.jackpots?.enabled || false;
      default:
        return false;
    }
  };

  // Helper function to check if bonus features can be enabled (symbol-specific)
  const canEnableBonusFeatures = (): boolean => {
    const existingBonusSymbols = config?.theme?.generated?.bonusSymbols || {};
    return Object.values(existingBonusSymbols).some(url => url && url.length > 0);
  };

  // Helper function to check if a specific symbol type is generated
  const hasSymbolType = (symbolType: 'bonus' | 'free' | 'jackpot'): boolean => {
    const existingBonusSymbols = config?.theme?.generated?.bonusSymbols || {};
    const storageKey = getBonusSymbolStorageKey(symbolType);
    const symbolUrl = existingBonusSymbols[storageKey];
    return !!(symbolUrl && symbolUrl.length > 0);
  };

  // Helper function to check if a specific feature can be enabled based on required symbol
  const canEnableSpecificFeature = (featureType: string): { canEnable: boolean; requiredSymbol: string; symbolName: string } => {
    switch (featureType) {
      case 'freeSpins':
        return {
          canEnable: hasSymbolType('free'),
          requiredSymbol: 'free',
          symbolName: 'Free Spins'
        };
      case 'holdAndSpin':
        return {
          canEnable: hasSymbolType('free'),
          requiredSymbol: 'free',
          symbolName: 'Free Spins'
        };
      case 'pickAndClick':
        return {
          canEnable: hasSymbolType('bonus'),
          requiredSymbol: 'bonus',
          symbolName: 'Bonus'
        };
      case 'wheel':
        return {
          canEnable: hasSymbolType('bonus'),
          requiredSymbol: 'bonus',
          symbolName: 'Bonus'
        };
      case 'jackpots':
        return {
          canEnable: hasSymbolType('bonus'),
          requiredSymbol: 'bonus',
          symbolName: 'Bonus'
        };
      default:
        return {
          canEnable: false,
          requiredSymbol: 'unknown',
          symbolName: 'Unknown'
        };
    }
  };

  // Helper to update a specific preview state
  const updatePreviewState = (feature: string, value: boolean) => {
    if (feature === 'wheel' || feature === 'pickAndClick' || feature === 'holdAndSpin') {
      setPreviewStates(prev => ({
        ...prev,
        [feature]: value
      }));

      if (value) {
        setActivePreview(feature);
      }
    }
  };

  const toggleFeatureEnabled = (feature: string) => {
    const currentBonus = config.bonus || {};

    // Check if trying to enable a feature without the required specific symbol
    if (!isFeatureEnabled(feature)) {
      const featureValidation = canEnableSpecificFeature(feature);
      if (!featureValidation.canEnable) {
        alert(`Please generate the ${featureValidation.symbolName} symbol to enable this feature.`);
        return;
      }
    }

    switch (feature) {
      case 'freeSpins':
        updateConfig({
          bonus: {
            ...currentBonus,
            freeSpins: {
              enabled: !isFeatureEnabled(feature),
              count: currentBonus.freeSpins?.count || 10,
              triggers: currentBonus.freeSpins?.triggers || [3],
              multipliers: currentBonus.freeSpins?.multipliers || [1],
              retriggers: currentBonus.freeSpins?.retriggers || true
            }
          }
        });
        break;
      case 'pickAndClick':
        updateConfig({
          bonus: {
            ...currentBonus,
            pickAndClick: {
              enabled: !isFeatureEnabled(feature),
              gridSize: (currentBonus.pickAndClick?.gridSize || [3, 3]) as [number, number],
              picks: currentBonus.pickAndClick?.picks || 3,
              maxPrize: currentBonus.pickAndClick?.maxPrize || 100,
              extraPicks: currentBonus.pickAndClick?.extraPicks || false,
              multipliers: currentBonus.pickAndClick?.multipliers || false
            }
          }
        });
        break;
      case 'wheel':
        updateConfig({
          bonus: {
            ...currentBonus,
            wheel: {
              enabled: !isFeatureEnabled(feature),
              segments: currentBonus.wheel?.segments || 8,
              maxMultiplier: currentBonus.wheel?.maxMultiplier || 50,
              levelUp: currentBonus.wheel?.levelUp || false,
              respin: currentBonus.wheel?.respin || false
            }
          }
        });
        break;
      case 'holdAndSpin':
        updateConfig({
          bonus: {
            ...currentBonus,
            holdAndSpin: {
              enabled: !isFeatureEnabled(feature),
              gridSize: (currentBonus.holdAndSpin?.gridSize || [3, 3]) as [number, number],
              initialRespins: currentBonus.holdAndSpin?.initialRespins || 3,
              maxSymbolValue: currentBonus.holdAndSpin?.maxSymbolValue || 100,
              resetRespins: currentBonus.holdAndSpin?.resetRespins || false,
              collectAll: currentBonus.holdAndSpin?.collectAll || false
            }
          }
        });
        break;
      case 'jackpots':
        updateConfig({
          bonus: {
            ...currentBonus,
            jackpots: {
              enabled: !isFeatureEnabled(feature),
              type: currentBonus.jackpots?.type || 'fixed',
              levels: currentBonus.jackpots?.levels || ['Mini', 'Minor', 'Major', 'Grand'],
              trigger: currentBonus.jackpots?.trigger || 'random',
              values: currentBonus.jackpots?.values || { Mini: 10, Minor: 50, Major: 250, Grand: 1000 }
            }
          }
        });
        break;
    }

    // When enabling a feature, automatically show its preview
    if (!isFeatureEnabled(feature)) {
      if (feature === 'wheel' || feature === 'pickAndClick' || feature === 'holdAndSpin') {
        updatePreviewState(feature, true);
      }
    }
  };

  // Initialize preview states based on enabled features
  useEffect(() => {
    setPreviewStates({
      wheel: isFeatureEnabled('wheel'),
      pickAndClick: isFeatureEnabled('pickAndClick'),
      holdAndSpin: isFeatureEnabled('holdAndSpin')
    });

    // Set active preview to the first enabled feature
    if (isFeatureEnabled('wheel')) {
      setActivePreview('wheel');
    } else if (isFeatureEnabled('pickAndClick')) {
      setActivePreview('pickAndClick');
    } else if (isFeatureEnabled('holdAndSpin')) {
      setActivePreview('holdAndSpin');
    }
  }, []);

  // Render wheel preview when wheel state is active
  useEffect(() => {
    if (previewStates.wheel && isFeatureEnabled('wheel') && bonus?.wheel?.enabled) {
      const segmentCount = bonus?.wheel?.segments || 8;
      const hasLevelUp = !!bonus?.wheel?.levelUp;
      const hasRespin = !!bonus?.wheel?.respin;
      drawWheel(segmentCount, hasLevelUp, hasRespin);
    }
  }, [previewStates.wheel, bonus?.wheel?.segments, bonus?.wheel?.levelUp, bonus?.wheel?.respin, bonus?.wheel?.enabled]);

  // Function to draw Hold & Spin preview
  const drawHoldAndSpin = () => {
    const canvas = holdSpinCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw background
    ctx.fillStyle = '#0F1423';
    ctx.fillRect(0, 0, width, height);

    // Get grid size
    const gridSize = bonus?.holdAndSpin?.gridSize || [3, 3];
    const rows = gridSize[0];
    const cols = gridSize[1];

    // Calculate cell size
    const padding = 20;
    const cellWidth = (width - padding * 2) / cols;
    const cellHeight = (height - padding * 2) / rows;

    // Generate random symbols and locked state
    const symbols: number[][] = [];
    const locked: boolean[][] = [];

    // Generate symbol values with distribution
    const maxValue = bonus?.holdAndSpin?.maxSymbolValue || 100;

    for (let r = 0; r < rows; r++) {
      symbols[r] = [];
      locked[r] = [];
      for (let c = 0; c < cols; c++) {
        // 50% chance of locked symbols (already held)
        locked[r][c] = Math.random() < 0.5;

        // Generate symbol value
        if (locked[r][c]) {
          // For locked symbols, use value distribution
          if (Math.random() < 0.7) {
            symbols[r][c] = Math.floor(Math.random() * (maxValue * 0.3) + 1); // Low value
          } else if (Math.random() < 0.9) {
            symbols[r][c] = Math.floor(Math.random() * (maxValue * 0.4) + (maxValue * 0.3)); // Medium value
          } else {
            symbols[r][c] = Math.floor(Math.random() * (maxValue * 0.3) + (maxValue * 0.7)); // High value
          }
        } else {
          symbols[r][c] = 0; // Empty cells
        }
      }
    }

    // Draw the grid
    for (let r = 0; r < rows; r++) {
      for (let c = 0; c < cols; c++) {
        const x = padding + c * cellWidth;
        const y = padding + r * cellHeight;
        const value = symbols[r][c];
        const isLocked = locked[r][c];

        // Draw cell background
        ctx.fillStyle = isLocked ? '#1A5276' : '#2C3E50';
        ctx.beginPath();
        ctx.roundRect(x + 5, y + 5, cellWidth - 10, cellHeight - 10, 8);
        ctx.fill();

        if (isLocked) {
          // Draw value for locked symbols
          ctx.fillStyle = value < (maxValue * 0.3)
            ? '#3498DB' // Low value color
            : value < (maxValue * 0.7)
              ? '#E74C3C' // Medium value color
              : '#F1C40F'; // High value color

          ctx.beginPath();
          ctx.arc(x + cellWidth / 2, y + cellHeight / 2, cellWidth * 0.35, 0, Math.PI * 2);
          ctx.fill();

          // Draw symbol value
          ctx.fillStyle = '#FFFFFF';
          ctx.font = 'bold 16px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText(`${value}x`, x + cellWidth / 2, y + cellHeight / 2);
        } else {
          // Draw empty slot
          ctx.strokeStyle = '#95A5A6';
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.arc(x + cellWidth / 2, y + cellHeight / 2, cellWidth * 0.2, 0, Math.PI * 2);
          ctx.stroke();
        }
      }
    }

    // Draw header
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    ctx.fillText('HOLD & SPIN BONUS', width / 2, 8);

    // Draw respins info
    ctx.font = '12px Arial';
    ctx.fillText(`RESPINS: ${bonus?.holdAndSpin?.initialRespins || 3}`, width / 2, height - 20);
  }

  // Render Hold & Spin preview when holdAndSpin state is active
  useEffect(() => {
    if (previewStates.holdAndSpin && isFeatureEnabled('holdAndSpin')) {
      drawHoldAndSpin();
    }
  }, [previewStates.holdAndSpin, bonus?.holdAndSpin?.gridSize, bonus?.holdAndSpin?.initialRespins, bonus?.holdAndSpin?.maxSymbolValue]);

  // Calculate math model based on selected features
  useEffect(() => {
    let totalRTP = 0;
    let totalHitFrequency = 0;
    let maxWinPotential = 0;

    // Free Spins contribution
    if (bonus?.freeSpins?.enabled) {
      const multiplier = Math.max(...(bonus.freeSpins.multipliers || [1]));
      const spinsCount = bonus.freeSpins.count || 10;
      totalRTP += (multiplier * spinsCount * 0.1);
      totalHitFrequency += 1 / 165; // Base hit rate
      maxWinPotential = Math.max(maxWinPotential, multiplier * spinsCount * 100);
    }

    // Pick & Click contribution
    if (bonus?.pickAndClick?.enabled) {
      const picks = bonus.pickAndClick.picks || 3;
      const maxPrize = bonus.pickAndClick.maxPrize || 100;
      totalRTP += (picks * maxPrize * 0.05);
      totalHitFrequency += 1 / 200;
      maxWinPotential = Math.max(maxWinPotential, maxPrize);
    }

    // Wheel Bonus contribution
    if (bonus?.wheel?.enabled) {
      const segments = bonus.wheel.segments || 8;
      const maxMultiplier = bonus.wheel.maxMultiplier || 50;
      totalRTP += (maxMultiplier * 0.2);
      totalHitFrequency += 1 / 250;
      maxWinPotential = Math.max(maxWinPotential, maxMultiplier * 100);
    }

    // Hold & Spin contribution
    if (bonus?.holdAndSpin?.enabled) {
      const positions = (bonus.holdAndSpin.gridSize?.[0] || 3) * (bonus.holdAndSpin.gridSize?.[1] || 3);
      const maxValue = bonus.holdAndSpin.maxSymbolValue || 100;
      totalRTP += (positions * maxValue * 0.02);
      totalHitFrequency += 1 / 180;
      maxWinPotential = Math.max(maxWinPotential, positions * maxValue);
    }

    // Jackpots contribution
    if (bonus?.jackpots?.enabled) {
      const jackpotLevels = bonus.jackpots.levels || ['Minor', 'Major'];
      const isProgressive = bonus.jackpots.type === 'progressive';

      // Calculate based on jackpot levels
      const baseContribution = isProgressive ? 6 : 4; // Progressive jackpots contribute more to RTP
      totalRTP += baseContribution * jackpotLevels.length * 0.5;

      // Higher hit frequency for more levels
      totalHitFrequency += jackpotLevels.length / 1000;

      // Max win potential for highest jackpot
      const highestLevel = jackpotLevels[jackpotLevels.length - 1];
      const jackpotValue =
        highestLevel === 'Mini' ? 20 :
          highestLevel === 'Minor' ? 100 :
            highestLevel === 'Major' ? 1000 :
              highestLevel === 'Grand' ? 10000 : 1000;

      maxWinPotential = Math.max(maxWinPotential, jackpotValue);
    }

    setMathModel({
      featureRTP: totalRTP,
      hitFrequency: totalHitFrequency,
      maxWin: maxWinPotential
    });
  }, [bonus]);

  return (
    <div className="space-y-2 rounded-md">
      {/* Bonus Features */}
      <div className='border bg-white rounded-md'>
        <div
          className="w-full bg-gray-50 border-l-4 border-l-red-500 p-3 flex items-center justify-between text-left transition-colors"
        >
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-gray-900">Select Bonus Symbol Type</h3>
          </div>
        </div>
        <div className="p-3">
          {/* Symbol Carousel */}
          <div className="mb-2 ">
            <div className="flex gap-3 overflow-x-auto p-2">
              {bonusSymbols.map((symbol) => (
                <BonusSymbolCarousel
                  key={symbol.id}
                  symbol={symbol}
                  isSelected={selectedSymbolId === symbol.id}
                  onClick={() => handleSymbolSelection(symbol.id)}
                  isGenerating={isGenerating && selectedSymbolId === symbol.id}
                  progress={0}
                />
              ))}
            </div>
          </div>
          {/* Bonus Symbol Generation Section */}
          <div className="space-y-3 bg-white rounded-md">
            <p className="text-sm text-[#5E6C84]">Generate bonus symbols required for bonus features. At least one symbol must be generated to enable features.</p>
            {/* Symbol Selection Tabs */}
            {/* <div className="flex gap-2 flex-wrap">
                {bonusSymbols.map((symbol) => {
                  const preset = BONUS_SYMBOL_PRESETS.find(p => p.type === symbol.gameSymbolType);
                  const isEnabled = preset?.enabled || false;

                  return (
                    <button
                      key={symbol.id}
                      onClick={() => {
                        if (isEnabled) {
                          setSelectedSymbolId(symbol.id);
                          setPrompt(symbol.prompt);
                          setContentType(`symbol-${symbol.gameSymbolType}` as any);
                        }
                      }}
                      disabled={!isEnabled}
                      className={`px-3 py-2 rounded-lg border text-sm transition-colors ${
                        !isEnabled
                          ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                          : selectedSymbolId === symbol.id
                            ? 'bg-[#0052CC] text-white border-[#0052CC]'
                            : 'bg-white text-[#172B4D] border-[#DFE1E6] hover:border-[#0052CC]'
                      }`}
                    >
                      {symbol.name}
                      {symbol.imageUrl && isEnabled && <span className="ml-1 text-green-500">✓</span>}
                      {!isEnabled && <span className="ml-1 text-gray-400">(Coming Soon)</span>}
                    </button>
                  );
                })}
              </div> */}

            {selectedSymbol && (
              <div className="space-y-3 ">

                {/* Content Type Selection */}
                <div className="space-y-2 border rounded-md bg-gray-50 p-2">
                  <label className="block font-medium text-[#172B4D]">Content Type</label>
                  <div className="flex flex-wrap gap-4">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        checked={contentType === 'symbol-bonus'}
                        onChange={() => setContentType('symbol-bonus')}
                        className="w-4 h-4 text-[#0052CC]"
                      />
                      <span className="text-sm text-[#172B4D]">Symbol + Bonus (5 letters)</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        checked={contentType === 'symbol-free'}
                        onChange={() => setContentType('symbol-free')}
                        className="w-4 h-4 text-[#0052CC]"
                      />
                      <span className="text-sm text-[#172B4D]">Symbol + Free (4 letters)</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        checked={contentType === 'text-only'}
                        onChange={() => setContentType('text-only')}
                        className="w-4 h-4 text-[#0052CC]"
                      />
                      <span className="text-sm text-[#172B4D]">Text Only</span>
                    </label>
                  </div>
                </div>

                {/* Layout Templates */}
                <div className="space-y-2 border rounded-md p-2 bg-gray-50">
                  <label className="block text-sm font-medium text-[#172B4D]">Layout Templates</label>
                  <p className="text-xs text-[#5E6C84] mb-3">Choose how text and symbols are arranged in your symbol</p>
                  <div className="flex flex-wrap gap-3">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        checked={selectedLayoutTemplate === 'text-top'}
                        onChange={() => setSelectedLayoutTemplate('text-top')}
                        className="w-4 h-4 text-[#0052CC]"
                      />
                      <div className="flex items-center gap-1">
                        <span className="text-sm">🔤</span>
                        <div>
                          <div className="text-sm font-medium text-[#172B4D]">Text on Top</div>
                          <div className="text-xs text-[#5E6C84]">Text above, symbol below</div>
                        </div>
                      </div>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        checked={selectedLayoutTemplate === 'text-bottom'}
                        onChange={() => setSelectedLayoutTemplate('text-bottom')}
                        className="w-4 h-4 text-[#0052CC]"
                      />
                      <div className="flex items-center gap-1">
                        <span className="text-sm">🔽</span>
                        <div>
                          <div className="text-sm font-medium text-[#172B4D]">Text on Bottom</div>
                          <div className="text-xs text-[#5E6C84]">Large symbol above, text below</div>
                        </div>
                      </div>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        checked={selectedLayoutTemplate === 'text-overlay'}
                        onChange={() => setSelectedLayoutTemplate('text-overlay')}
                        className="w-4 h-4 text-[#0052CC]"
                      />
                      <div className="flex items-center gap-1">
                        <span className="text-sm">📝</span>
                        <div>
                          <div className="text-sm font-medium text-[#172B4D]">Text Overlay</div>
                          <div className="text-xs text-[#5E6C84]">Text overlaid on symbol</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Prompt Input */}
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Generate {selectedSymbol.name} Symbol
                  </h3>

                  <textarea
                    value={prompt}
                    onChange={(e) => handlePromptChange(e.target.value)}
                    placeholder="Describe the symbol you want to create..."
                    className="w-full h-24 p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  />

                  <Button
                    variant='generate'
                    onClick={handleGenerateSymbol}
                    className='w-full'
                    disabled={isGenerating || !prompt.trim()}
                  >
                    {isGenerating ? (
                      <>
                        <Loader className="w-5 h-5 animate-spin" />
                        Generating {selectedSymbol.name}...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-5 h-5" />
                        Generate {selectedSymbol.name}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

      </div>
      <div className="bg-white rounded-md p-0 border border-[#DFE1E6] shadow-sm">
        <div
          className="w-full bg-gray-50 border-l-4 border-l-red-500 p-3 flex items-center justify-between text-left transition-colors"
        >
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-gray-900">Bonus Features</h3>
          </div>
        </div>

        <div className="p-3 space-y-2 bg-white rounded-md">
          {/* Free Spins */}
          <div className="bg-gray-50 rounded-md border border-[#DFE1E6] overflow-hidden shadow-sm">
            <div className="p-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-[#172B4D]">Free Spins</h4>
                    <p className="text-sm text-[#5E6C84]">Classic free spins bonus</p>
                    <p className="text-xs text-[#8993A4] mt-1">
                      Requires: <span className="font-medium">Free Spins symbol</span>
                      {hasSymbolType('free') ?
                        <span className="text-green-600 ml-1">✓ Generated</span> :
                        <span className="text-orange-600 ml-1">⚠ Not generated</span>
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center mr-2 p-1 px-2 border rounded-md bg-white gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={isFeatureEnabled('freeSpins')}
                      onChange={() => toggleFeatureEnabled('freeSpins')}
                      className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                    />
                    <span className="text-sm text-[#172B4D]">Enable</span>
                  </label>
                </div>
              </div>
            </div>

            {isFeatureEnabled('freeSpins') && (
              <div className="px-2 pb-4">
                <div className="pt-2 border-t border-[#DFE1E6]">
                  {/* Trigger Requirements */}
                  <div className='border p-2 rounded-md bg-white'>
                    <label className="block text-sm font-medium text-[#172B4D] mb-1">
                      Trigger Requirements
                    </label>
                    <select
                      value={bonus?.freeSpins?.triggers?.[0] || 3}
                      onChange={(e) => updateConfig({
                        bonus: {
                          ...config.bonus,
                          freeSpins: {
                            enabled: config.bonus?.freeSpins?.enabled || false,
                            count: config.bonus?.freeSpins?.count || 10,
                            triggers: [parseInt(e.target.value)],
                            multipliers: config.bonus?.freeSpins?.multipliers || [1],
                            retriggers: config.bonus?.freeSpins?.retriggers || true
                          }
                        }
                      })}
                      className="w-full bg-white border border-[#DFE1E6] rounded-lg px-4 py-2 text-[#172B4D]"
                    >
                      <option value="3">3 Scatters</option>
                      <option value="4">4 Scatters</option>
                      <option value="5">5 Scatters</option>
                    </select>
                  </div>

                  {/* Free Spins Count */}
                  <div className="mt-2 border bg-white rounded-md p-2">
                    <label className="block text-sm font-medium text-[#172B4D] mb-2">
                      Number of Free Spins
                    </label>
                    <div className="grid grid-cols-3 gap-4">
                      {[8, 10, 12, 15, 20, 25].map((count) => (
                        <button
                          key={count}
                          onClick={() => updateConfig({
                            bonus: {
                              ...config.bonus,
                              freeSpins: ensureBonusDefaults('freeSpins', { count })
                            }
                          })}
                          className={`p-2 rounded-lg border transition-colors ${bonus?.freeSpins?.count === count
                            ? 'bg-red-50 border-[#0052CC] border-red-500'
                            : 'bg-white border-[#DFE1E6]'
                            }`}
                        >
                          {count} Spins
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Multipliers */}
                  <div className="mt-2 border p-2 rounded-md bg-white">
                    <label className="block text-sm font-medium text-[#172B4D] mb-2">
                      Win Multipliers
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {[1, 2, 3, 5, 10].map((mult) => (
                        <label key={mult} className="flex items-center border py-1 px-2 rounded-md">
                          <input
                            type="checkbox"
                            checked={bonus?.freeSpins?.multipliers?.includes(mult)}
                            onChange={(e) => {
                              const newMults = e.target.checked
                                ? [...(bonus?.freeSpins?.multipliers || []), mult]
                                : (bonus?.freeSpins?.multipliers || []).filter(m => m !== mult);
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  freeSpins: ensureBonusDefaults('freeSpins', { multipliers: newMults })
                                }
                              });
                            }}
                            className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                          />
                          <span className="ml-2 text-[#172B4D]">x{mult}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Additional Options */}
                  <div className="mt-2 border bg-white p-2 rounded-md space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={bonus?.freeSpins?.retriggers}
                        onChange={(e) => updateConfig({
                          bonus: {
                            ...config.bonus,
                            freeSpins: ensureBonusDefaults('freeSpins', { retriggers: e.target.checked })
                          }
                        })}
                        className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                      />
                      <span className="ml-2 text-[#172B4D]">Allow Retriggers</span>
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
          {/* Pick & Click */}
          <div className="bg-gray-50 rounded-md border border-[#DFE1E6] overflow-hidden shadow-sm">
            <div className="p-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-[#172B4D]">Pick & Click</h4>
                    <p className="text-sm text-[#5E6C84]">Interactive bonus game</p>
                    <p className="text-xs text-[#8993A4] mt-1">
                      Requires: <span className="font-medium">Bonus symbol</span>
                      {hasSymbolType('bonus') ?
                        <span className="text-green-600 ml-1">✓ Generated</span> :
                        <span className="text-orange-600 ml-1">⚠ Not generated</span>
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center mr-2 p-1 px-2 border rounded-md bg-white gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={isFeatureEnabled('pickAndClick')}
                      onChange={() => toggleFeatureEnabled('pickAndClick')}
                      className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                    />
                    <span className="text-sm text-[#172B4D]">Enable</span>
                  </label>
                </div>
              </div>
            </div>

            {isFeatureEnabled('pickAndClick') && (
              <div className="px-2 pb-2">
                <div className="pt-2 border-t border-[#DFE1E6]">
                  {/* Preview and Configuration in two columns */}
                  <div className="flex border p-1 rounded-md bg-white flex-col md:flex-row gap-6">
                    {/* Left column - Configuration */}
                    <div className="w-full md:w-1/2 space-y-2">
                      {/* Grid Size */}
                      <div>
                        <label className="block text-sm font-medium text-[#172B4D] mb-2">
                          Grid Size
                        </label>
                        <div className="grid grid-cols-3 gap-2 border p-1 bg-gray-50 rounded-md">
                          {[
                            { label: '3x3', size: [3, 3] },
                            { label: '3x4', size: [3, 4] },
                            { label: '4x4', size: [4, 4] }
                          ].map((grid) => (
                            <button
                              key={grid.label}
                              onClick={() => {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    pickAndClick: ensureBonusDefaults('pickAndClick', {
                                      gridSize: grid.size as [number, number]
                                    })
                                  }
                                });
                                // Only set active preview, don't affect other previews
                                if (activePreview !== 'pickAndClick') {
                                  setActivePreview('pickAndClick');
                                } else {
                                  // Refresh the pick & click preview
                                  const tempPreview = activePreview;
                                  setActivePreview(null);
                                  setTimeout(() => setActivePreview(tempPreview), 50);
                                }
                              }}
                              className={`p-1 rounded-lg border transition-colors ${JSON.stringify(bonus?.pickAndClick?.gridSize) === JSON.stringify(grid.size)
                                ? 'bg-red-50 border-red-500'
                                : 'bg-white '
                                }`}
                            >
                              {grid.label}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Number of Picks */}
                      <div className='border p-2 rounded-md bg-gray-50'>
                        <label className="block text-sm font-medium text-[#172B4D] mb-2">
                          Initial Picks
                        </label>
                        <div className="flex items-center">
                          <button
                            onClick={() => {
                              const currentPicks = bonus?.pickAndClick?.picks || 3;
                              if (currentPicks > 1) {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    pickAndClick: ensureBonusDefaults('pickAndClick', { picks: currentPicks - 1 })
                                  }
                                });
                                setActivePreview('pickAndClick');
                              }
                            }}
                            className="w-8 h-8 flex items-center justify-center bg-[#F4F5F7] rounded-l-lg border border-[#DFE1E6] text-[#172B4D] hover:bg-[#E9ECF0]"
                          >
                            <Minus className="w-4 h-4" />
                          </button>

                          <input
                            type="number"
                            min="1"
                            max="10"
                            value={bonus?.pickAndClick?.picks || 3}
                            onChange={(e) => {
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  pickAndClick: ensureBonusDefaults('pickAndClick', { picks: parseInt(e.target.value) })
                                }
                              });
                              setActivePreview('pickAndClick');
                            }}
                            className="flex-1 h-8 bg-white border-t border-b border-[#DFE1E6] px-4 text-center text-[#172B4D]"
                          />

                          <button
                            onClick={() => {
                              const currentPicks = bonus?.pickAndClick?.picks || 3;
                              if (currentPicks < 10) {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    pickAndClick: ensureBonusDefaults('pickAndClick', { picks: currentPicks + 1 })
                                  }
                                });
                                setActivePreview('pickAndClick');
                              }
                            }}
                            className="w-8 h-8 flex items-center justify-center bg-[#F4F5F7] rounded-r-lg border border-[#DFE1E6] text-[#172B4D] hover:bg-[#E9ECF0]"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                        </div>
                        <div className="flex justify-center mt-1 text-xs text-[#5E6C84]">
                          <span>Picks remaining: {bonus?.pickAndClick?.picks || 3}</span>
                        </div>
                      </div>

                      {/* Prize Configuration */}
                      <div className='border p-2 rounded-md bg-gray-50'>
                        <label className="block text-sm font-medium text-[#172B4D] mb-2">
                          Maximum Prize
                        </label>
                        <div className="grid grid-cols-4 gap-2">
                          {[50, 100, 200, 500].map((prize) => (
                            <button
                              key={prize}
                              onClick={() => {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    pickAndClick: ensureBonusDefaults('pickAndClick', { maxPrize: prize })
                                  }
                                });
                                setActivePreview('pickAndClick');
                              }}
                              className={`p-1 rounded-lg text-base border transition-colors ${bonus?.pickAndClick?.maxPrize === prize
                                ? ' bg-red-50 border-red-500'
                                : 'bg-white'
                                }`}
                            >
                              {prize}x
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Additional Features */}
                      <div className="space-y-2 border p-2 rounded-md bg-gray-50">
                        <label className="flex items-center border p-1 rounded-md bg-white">
                          <input
                            type="checkbox"
                            checked={bonus?.pickAndClick?.extraPicks}
                            onChange={(e) => {
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  pickAndClick: ensureBonusDefaults('pickAndClick', { extraPicks: e.target.checked })
                                }
                              });
                              setActivePreview('pickAndClick');
                            }}
                            className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                          />
                          <span className="ml-2 text-[#172B4D]">Include Extra Pick Symbols</span>
                          <Plus className="ml-2 w-4 h-4 text-[#66BB6A]" />
                        </label>
                        <label className="flex items-center border p-1 rounded-md bg-white">
                          <input
                            type="checkbox"
                            checked={bonus?.pickAndClick?.multipliers}
                            onChange={(e) => {
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  pickAndClick: ensureBonusDefaults('pickAndClick', { multipliers: e.target.checked })
                                }
                              });
                              setActivePreview('pickAndClick');
                            }}
                            className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                          />
                          <span className="ml-2 text-[#172B4D]">Include Multiplier Symbols</span>
                          <span className="ml-2 text-xs font-bold text-[#FFA726]">x2</span>
                        </label>
                      </div>
                    </div>

                    {/* Right column - Preview */}
                    <div className="w-full md:w-1/2 ">
                      <div className="flex flex-col items-center">
                        <div className="mb-2 text-sm font-medium text-[#172B4D] flex items-center">
                          <span>Live Preview</span>
                          {!previewStates.pickAndClick && (
                            <button
                              onClick={() => updatePreviewState('pickAndClick', true)}
                              className="ml-2 text-[#0052CC] hover:text-[#0747A6] transition-colors"
                            >
                              (Show)
                            </button>
                          )}
                        </div>

                        {previewStates.pickAndClick && activePreview === 'pickAndClick' ? (
                          <div>
                            {/* Render Pick & Click grid preview */}
                            {(() => {
                              const { grid, revealedCells, picks } = renderPickAndClickGrid();
                              const gridSize = bonus?.pickAndClick?.gridSize || [3, 3];
                              const rows = gridSize[0];
                              const cols = gridSize[1];

                              return (
                                <div className="bg-[#0F1423] p-2 rounded-md">
                                  <div className="flex justify-between items-center mb-4">
                                    <div className="text-white font-semibold">PICK & CLICK BONUS</div>
                                    <div className="text-[#FFF176] font-semibold">Picks: {picks}</div>
                                  </div>

                                  <div
                                    className="grid gap-2 mx-auto"
                                    style={{
                                      gridTemplateColumns: `repeat(${cols}, 1fr)`,
                                      gridTemplateRows: `repeat(${rows}, 1fr)`
                                    }}
                                  >
                                    {Array(rows).fill(0).map((_, r) => (
                                      Array(cols).fill(0).map((_, c) => {
                                        const cell = grid[r][c];
                                        const isRevealed = revealedCells[r][c];

                                        if (isRevealed && cell) {
                                          // Render revealed cell with different styles based on type
                                          let content, bgColor;

                                          if (cell.type === 'extraPick') {
                                            content = (
                                              <div className="flex flex-col items-center text-center justify-center">
                                                <Plus className="w-4 h-4 text-white" />
                                                <p className="text-xs mt-1 text-white">EXTRA PICK</p>
                                              </div>
                                            );
                                            bgColor = 'bg-[#66BB6A]';
                                          } else if (cell.type === 'multiplier') {
                                            content = (
                                              <div className="flex flex-col items-center justify-center">
                                                <div className="text-base font-bold text-white">x{cell.value}</div>
                                                <div className="text-xs text-white">MULTIPLIER</div>
                                              </div>
                                            );
                                            bgColor = 'bg-[#FFA726]';
                                          } else {
                                            content = (
                                              <div className="flex flex-col items-center justify-center">
                                                <div className="text-base font-bold text-white">{cell.value}x</div>
                                                <div className="text-xs  text-white">WIN</div>
                                              </div>
                                            );
                                            const maxPrize = bonus?.pickAndClick?.maxPrize || 100;
                                            bgColor = cell.value < (maxPrize * 0.3)
                                              ? 'bg-[#5C6BC0]' // Low prize
                                              : cell.value < (maxPrize * 0.7)
                                                ? 'bg-[#EF5350]' // Medium prize
                                                : 'bg-[#FFD700]'; // High prize
                                          }

                                          return (
                                            <div
                                              key={`${r}-${c}`}
                                              className={`${bgColor} w-12 h-12 rounded-lg shadow-md flex items-center justify-center text-white transition-all duration-300 transform hover:scale-105`}
                                            >
                                              {content}
                                            </div>
                                          );
                                        } else {
                                          // Render unrevealed cell
                                          return (
                                            <div
                                              key={`${r}-${c}`}
                                              className="w-12 h-12 bg-[#2D3748] rounded-lg flex items-center justify-center text-white cursor-pointer shadow-md transition-all duration-300 hover:bg-[#4A5568]"
                                              onClick={() => setActivePreview('pickAndClick')} // Re-render to show a different random setup
                                            >
                                              <div className="text-3xl font-bold text-[#A0AEC0]">?</div>
                                            </div>
                                          );
                                        }
                                      })
                                    ))}
                                  </div>

                                  <div className="flex justify-center mt-3">
                                    <button
                                      onClick={() => {
                                        // Just call renderPickAndClickGrid again by refreshing state
                                        const tempPreview = activePreview;
                                        setActivePreview(null);
                                        setTimeout(() => setActivePreview(tempPreview), 50);
                                      }}
                                      className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
                                    >
                                      Randomize Preview
                                    </button>
                                  </div>
                                </div>
                              );
                            })()}
                          </div>
                        ) : (
                          <div
                            className="w-[300px] h-[300px] border border-dashed border-[#DFE1E6] rounded-lg flex items-center justify-center bg-[#F4F5F7] cursor-pointer hover:bg-[#DEEBFF] transition-colors"
                            onClick={() => updatePreviewState('pickAndClick', true)}
                          >
                            <div className="text-center p-5">
                              <Gift className="w-10 h-10 text-[#0052CC] mx-auto mb-2" />
                              <p className="text-[#172B4D] font-medium">Click to preview your Pick & Click Bonus</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Insights & Tips */}
                  {/* <div className="mt-6 bg-[#F4F5F7] p-4 rounded-lg text-sm text-[#5E6C84]">
                    <h5 className="font-medium text-[#172B4D] mb-1">Design Insights</h5>
                    <ul className="space-y-1 list-disc pl-5">
                      <li>Larger grids give more possibilities but reduce odds of high-value prizes</li>
                      <li>Extra Pick symbols extend the bonus and increase win potential</li>
                      <li>Multipliers add depth to gameplay and can lead to surprise big wins</li>
                    </ul>
                  </div> */}
                </div>
              </div>
            )}
          </div>

          {/* Wheel Bonus */}
          <div className="bg-gray-50 rounded-md border border-[#DFE1E6] overflow-hidden shadow-sm">
            <div className="p-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-[#172B4D]">Wheel Bonus</h4>
                    <p className="text-sm text-[#5E6C84]">Wheel of fortune style bonus</p>
                    <p className="text-xs text-[#8993A4] mt-1">
                      Requires: <span className="font-medium">Bonus symbol</span>
                      {hasSymbolType('bonus') ?
                        <span className="text-green-600 ml-1">✓ Generated</span> :
                        <span className="text-orange-600 ml-1">⚠ Not generated</span>
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center mr-2 p-1 px-2 border rounded-md bg-white gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={isFeatureEnabled('wheel')}
                      onChange={() => toggleFeatureEnabled('wheel')}
                      className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                    />
                    <span className="text-sm text-[#172B4D]">Enable</span>
                  </label>
                </div>
              </div>
            </div>

            {isFeatureEnabled('wheel') && (
              <div className="px-2 pb-2">
                <div className="pt-2 border-t border-[#DFE1E6]">
                  {/* Preview and Configuration in two columns */}
                  <div className="flex border p-2 rounded-md bg-white flex-col md:flex-row gap-6">
                    {/* Left column - Configuration */}
                    <div className="w-full md:w-1/2 space-y-2">
                      {/* Number of Segments */}
                      <div className='border p-2 rounded-md bg-gray-50'>
                        <label className="block text-sm font-medium text-[#172B4D] mb-1">
                          Number of Segments
                        </label>
                        <div className="grid grid-cols-4 gap-2">
                          {[8, 12, 16, 20].map((segments) => (
                            <button
                              key={segments}
                              onClick={() => {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    wheel: ensureBonusDefaults('wheel', { segments })
                                  }
                                });
                                setActivePreview('wheel');
                              }}
                              className={`p-1 rounded-lg border transition-colors ${bonus?.wheel?.segments === segments
                                ? 'bg-red-50 border-red-500'
                                : 'bg-white ]'
                                }`}
                            >
                              {segments}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Maximum Multiplier */}
                      <div className='border p-2 rounded-md bg-gray-50'>
                        <label className="block text-sm font-medium text-[#172B4D] mb-2">
                          Maximum Multiplier
                        </label>
                        <div className="grid grid-cols-4 gap-2">
                          {[50, 100, 250, 500].map((mult) => (
                            <button
                              key={mult}
                              onClick={() => {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    wheel: ensureBonusDefaults('wheel', { maxMultiplier: mult })
                                  }
                                });
                                setActivePreview('wheel');
                              }}
                              className={`p-1 rounded-lg border transition-colors ${bonus?.wheel?.maxMultiplier === mult
                                ? 'bg-red-50 border-red-500'
                                : 'bg-white'
                                }`}
                            >
                              {mult}x
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Additional Features */}
                      <div className="space-y-2 border p-2 rounded-md bg-gray-50">
                        <label className="flex items-center border p-1 rounded-md bg-white">
                          <input
                            type="checkbox"
                            checked={bonus?.wheel?.levelUp}
                            onChange={(e) => {
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  wheel: ensureBonusDefaults('wheel', { levelUp: e.target.checked })
                                }
                              });
                              setActivePreview('wheel');
                            }}
                            className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC]"
                          />
                          <span className="ml-2 text-base text-[#172B4D]">Include Level Up Segments</span>
                          {/* <Award className="ml-2 w-4 h-4 text-[#FFD700]" /> */}
                        </label>
                        <label className="flex items-center border p-1 rounded-md bg-white">
                          <input
                            type="checkbox"
                            checked={bonus?.wheel?.respin}
                            onChange={(e) => {
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  wheel: ensureBonusDefaults('wheel', { respin: e.target.checked })
                                }
                              });
                              setActivePreview('wheel');
                            }}
                            className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                          />
                          <span className="ml-2 text-base text-[#172B4D]">Include Respin Segments</span>
                        </label>
                      </div>
                    </div>

                    {/* Right column - Preview */}
                    <div className="w-full md:w-1/2">
                      <div className="flex flex-col items-center">
                        <div className="mb-2 text-sm font-medium text-[#172B4D] flex items-center">
                          <span>Live Preview</span>
                          {!previewStates.wheel && (
                            <button
                              onClick={() => updatePreviewState('wheel', true)}
                              className="ml-2 text-[#0052CC] hover:text-[#0747A6] transition-colors"
                            >
                              (Show)
                            </button>
                          )}
                        </div>
                        {previewStates.wheel && activePreview === 'wheel' ? (
                          <div className="relative">
                            <canvas
                              ref={wheelCanvasRef}
                              width={250}
                              height={250}
                              className="border border-[#DFE1E6] rounded-full shadow-sm"
                            />
                            <div className="absolute inset-0 flex items-center justify-center">
                              <button
                                onClick={() => {
                                  const segmentCount = bonus?.wheel?.segments || 8;
                                  const hasLevelUp = !!bonus?.wheel?.levelUp;
                                  const hasRespin = !!bonus?.wheel?.respin;
                                  drawWheel(segmentCount, hasLevelUp, hasRespin);
                                }}
                                className="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
                              >
                                <RotateCw className="w-5 h-5 text-[#0052CC]" />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div
                            className="w-[300px] h-[300px] border border-dashed border-[#DFE1E6] rounded-full flex items-center justify-center bg-[#F4F5F7] cursor-pointer hover:bg-[#DEEBFF] transition-colors"
                            onClick={() => {
                              updatePreviewState('wheel', true);
                            }}
                          >
                            <div className="text-center p-5">
                              <Zap className="w-10 h-10 text-[#0052CC] mx-auto mb-2" />
                              <p className="text-[#172B4D] font-medium">Click to preview your Wheel Bonus</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Insights & Tips */}
                  {/* <div className="mt-6 bg-[#F4F5F7] p-4 rounded-lg text-sm text-[#5E6C84]">
                    <h5 className="font-medium text-[#172B4D] mb-1">Design Insights</h5>
                    <ul className="space-y-1 list-disc pl-5">
                      <li>Wheels with more segments reduce the chance of landing on high-value prizes</li>
                      <li>Level Up segments can lead to multiple tiers with increasing prizes</li>
                      <li>Respin segments keep players engaged with extended bonus sessions</li>
                    </ul>
                  </div> */}
                </div>
              </div>
            )}
          </div>

          {/* Hold & Spin */}
          <div className="bg-gray-50 rounded-md border border-[#DFE1E6] overflow-hidden shadow-sm">
            <div className="p-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-[#172B4D]">Hold & Spin</h4>
                    <p className="text-sm text-[#5E6C84]">Respin feature with locked symbols</p>
                    <p className="text-xs text-[#8993A4] mt-1">
                      Requires: <span className="font-medium">Free Spins symbol</span>
                      {hasSymbolType('free') ?
                        <span className="text-green-600 ml-1">✓ Generated</span> :
                        <span className="text-orange-600 ml-1">⚠ Not generated</span>
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center mr-2 p-1 px-2 border rounded-md bg-white gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={isFeatureEnabled('holdAndSpin')}
                      onChange={() => toggleFeatureEnabled('holdAndSpin')}
                      className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                    />
                    <span className="text-sm text-[#172B4D]">Enable</span>
                  </label>
                </div>
              </div>
            </div>

            {isFeatureEnabled('holdAndSpin') && (
              <div className="px-2 pb-2">
                <div className="pt-2 border-t border-[#DFE1E6]">
                  {/* Preview and Configuration in two columns */}
                  <div className="flex border p-2 rounded-md bg-white flex-col md:flex-row gap-6">
                    {/* Left column - Configuration */}
                    <div className="w-full md:w-1/2 space-y-2">
                      {/* Grid Size */}
                      <div className='border p-2 rounded-md bg-gray-50'>
                        <label className="block text-sm font-medium text-[#172B4D] mb-2">
                          Grid Size
                        </label>
                        <div className="grid grid-cols-3 gap-4">
                          {[
                            { label: '3x3', size: [3, 3] },
                            { label: '3x4', size: [3, 4] },
                            { label: '4x4', size: [4, 4] }
                          ].map((grid) => (
                            <button
                              key={grid.label}
                              onClick={() => {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    holdAndSpin: ensureBonusDefaults('holdAndSpin', {
                                      gridSize: grid.size as [number, number]
                                    })
                                  }
                                });
                                setActivePreview('holdAndSpin');
                              }}
                              className={`p-1 rounded-lg border transition-colors ${JSON.stringify(bonus?.holdAndSpin?.gridSize) === JSON.stringify(grid.size)
                                ? 'bg-red-50 border-red-500'
                                : 'bg-white'
                                }`}
                            >
                              {grid.label}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Initial Respins */}
                      <div className="mt-2 border rounded-md bg-gray-50 p-2">
                        <label className="block text-sm font-medium text-[#172B4D] mb-2">
                          Initial Respins
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          value={bonus?.holdAndSpin?.initialRespins || 3}
                          onChange={(e) => {
                            updateConfig({
                              bonus: {
                                ...config.bonus,
                                holdAndSpin: ensureBonusDefaults('holdAndSpin', { initialRespins: parseInt(e.target.value) })
                              }
                            });
                            setActivePreview('holdAndSpin');
                          }}
                          className="w-full bg-white border border-[#DFE1E6] rounded-lg px-2 py-1 text-[#172B4D]"
                        />
                      </div>

                      {/* Symbol Values */}
                      <div className="mt-2 border rounded-md bg-gray-50 p-2">
                        <label className="block text-sm font-medium text-[#172B4D] mb-2">
                          Maximum Symbol Value
                        </label>
                        <div className="grid grid-cols-4 gap-4">
                          {[25, 50, 100, 250].map((value) => (
                            <button
                              key={value}
                              onClick={() => {
                                updateConfig({
                                  bonus: {
                                    ...config.bonus,
                                    holdAndSpin: ensureBonusDefaults('holdAndSpin', { maxSymbolValue: value })
                                  }
                                });
                                setActivePreview('holdAndSpin');
                              }}
                              className={`p-1 rounded-lg border transition-colors ${bonus?.holdAndSpin?.maxSymbolValue === value
                                ? 'bg-red-50 border-red-500'
                                : 'bg-white '
                                }`}
                            >
                              {value}x
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Additional Features */}
                      <div className="mt-2 border rounded-md p-2 bg-gray-50 space-y-2">
                        <label className="flex items-center border p-1 rounded-md bg-white">
                          <input
                            type="checkbox"
                            checked={bonus?.holdAndSpin?.resetRespins}
                            onChange={(e) => {
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  holdAndSpin: ensureBonusDefaults('holdAndSpin', { resetRespins: e.target.checked })
                                }
                              });
                              setActivePreview('holdAndSpin');
                            }}
                            className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                          />
                          <span className="ml-2 text-[#172B4D]">Reset Respins on Symbol Land</span>
                        </label>
                        <label className="flex items-center border p-1 rounded-md bg-white">
                          <input
                            type="checkbox"
                            checked={bonus?.holdAndSpin?.collectAll}
                            onChange={(e) => {
                              updateConfig({
                                bonus: {
                                  ...config.bonus,
                                  holdAndSpin: ensureBonusDefaults('holdAndSpin', { collectAll: e.target.checked })
                                }
                              });
                              setActivePreview('holdAndSpin');
                            }}
                            className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                          />
                          <span className="ml-2 text-[#172B4D]">Include Collect All Symbol</span>
                        </label>
                      </div>
                    </div>

                    {/* Right column - Preview */}
                    <div className="w-full md:w-1/2">
                      <div className="flex flex-col items-center">
                        <div className="mb-2 text-sm font-medium text-[#172B4D] flex items-center">
                          <span>Live Preview</span>
                          {!previewStates.holdAndSpin && (
                            <button
                              onClick={() => updatePreviewState('holdAndSpin', true)}
                              className="ml-2 text-[#0052CC] hover:text-[#0747A6] transition-colors"
                            >
                              (Show)
                            </button>
                          )}
                        </div>

                        {previewStates.holdAndSpin && activePreview === 'holdAndSpin' ? (
                          <div className="relative">
                            <canvas
                              ref={holdSpinCanvasRef}
                              width={260}
                              height={250}
                              className="border border-[#DFE1E6] rounded-lg shadow-sm"
                            />
                            <div className="absolute inset-0 flex items-center justify-center">
                              <button
                                onClick={() => drawHoldAndSpin()}
                                className="w-10 h-10 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
                              >
                                <RefreshCw className="w-5 h-5 text-[#0052CC]" />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div
                            className="w-[300px] h-[300px] border border-dashed border-[#DFE1E6] rounded-lg flex items-center justify-center bg-[#F4F5F7] cursor-pointer hover:bg-[#DEEBFF] transition-colors"
                            onClick={() => updatePreviewState('holdAndSpin', true)}
                          >
                            <div className="text-center p-5">
                              <Coins className="w-10 h-10 text-[#0052CC] mx-auto mb-2" />
                              <p className="text-[#172B4D] font-medium">Click to preview your Hold & Spin Bonus</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Insights & Tips */}
                  {/* <div className="mt-6 bg-[#F4F5F7] p-4 rounded-lg text-sm text-[#5E6C84]">
                    <h5 className="font-medium text-[#172B4D] mb-1">Design Insights</h5>
                    <ul className="space-y-1 list-disc pl-5">
                      <li>Hold & Spin features focus on filling the grid with special symbols</li>
                      <li>Resetting respins extends play time and increases excitement</li>
                      <li>Larger grids offer more potential but are harder to fill</li>
                    </ul>
                  </div> */}
                </div>
              </div>
            )}
          </div>

          {/* Jackpots */}
          <div className="bg-gray-50 rounded-md border border-[#DFE1E6] overflow-hidden shadow-sm">
            <div className="p-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div>
                    <h4 className="font-medium text-[#172B4D]">Jackpots</h4>
                    <p className="text-sm text-[#5E6C84]">Progressive or fixed jackpot prizes</p>
                    <p className="text-xs text-[#8993A4] mt-1">
                      Requires: <span className="font-medium">Bonus symbol</span>
                      {hasSymbolType('bonus') ?
                        <span className="text-green-600 ml-1">✓ Generated</span> :
                        <span className="text-orange-600 ml-1">⚠ Not generated</span>
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center mr-2 p-1 px-2 border rounded-md bg-white gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={isFeatureEnabled('jackpots')}
                      onChange={() => toggleFeatureEnabled('jackpots')}
                      className="w-4 h-4 rounded border-[#DFE1E6] text-[#0052CC] focus:ring-[#0052CC]"
                    />
                    <span className="text-sm text-[#172B4D]">Enable</span>
                  </label>
                </div>
              </div>
            </div>

            {isFeatureEnabled('jackpots') && (
              <div className="px-2 pb-2">
                <div className="pt-2 border-t border-[#DFE1E6]">
                  {/* Jackpot Type */}
                  <div className='flex gap-4'>
                    <div className='border p-2 rounded-md bg-white w-full'>
                      <label className="block text-sm font-medium text-[#172B4D] mb-2">
                        Jackpot Type
                      </label>
                      <div className="grid grid-cols-1 gap-2">
                        {['Fixed', 'Progressive'].map((type) => (
                          <button
                            key={type}
                            onClick={() => updateConfig({
                              bonus: {
                                ...config.bonus,
                                jackpots: ensureBonusDefaults('jackpots', {
                                  type: type.toLowerCase() as 'fixed' | 'progressive'
                                })
                              }
                            })}
                            className={`p-2 rounded-lg border transition-colors ${config.bonus?.jackpots?.type === type.toLowerCase()
                              ? 'bg-red-50 border-red-500'
                              : 'bg-white'
                              }`}
                          >
                            {type}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Jackpot Levels */}
                    <div className="border p-2 rounded-md bg-white w-full">
                      <label className="block text-sm font-medium text-[#172B4D] mb-2">
                        Jackpot Levels
                      </label>
                      <div className="grid grid-cols-1 gap-2">
                        {[
                          { label: '2 Levels', levels: ['Minor', 'Major'] },
                          { label: '4 Levels', levels: ['Mini', 'Minor', 'Major', 'Grand'] }
                        ].map((option) => (
                          <button
                            key={option.label}
                            onClick={() => updateConfig({
                              bonus: {
                                ...config.bonus,
                                jackpots: ensureBonusDefaults('jackpots', { levels: option.levels })
                              }
                            })}
                            className={`p-2 rounded-lg border transition-colors ${JSON.stringify(config.bonus?.jackpots?.levels) === JSON.stringify(option.levels)
                              ? 'bg-red-50 border-red-500'
                              : 'bg-white'
                              }`}
                          >
                            {option.label}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Trigger Mechanism */}
                  <div className="mt-2 border rounded-md bg-white p-2">
                    <label className="block text-sm font-medium text-[#172B4D] mb-2">
                      Trigger Mechanism
                    </label>
                    <select
                      value={config.bonus?.jackpots?.trigger || 'random'}
                      onChange={(e) => updateConfig({
                        bonus: {
                          ...config.bonus,
                          jackpots: ensureBonusDefaults('jackpots', {
                            trigger: e.target.value as 'symbol' | 'bonus' | 'random'
                          })
                        }
                      })}
                      className="w-full bg-white border border-[#DFE1E6] rounded-lg px-4 py-2 text-[#172B4D]"
                    >
                      <option value="random">Random (Mystery)</option>
                      <option value="symbol">Dedicated Symbols</option>
                      <option value="bonus">Bonus Feature</option>
                    </select>
                  </div>

                  {/* Max Jackpot Values */}
                  {config.bonus?.jackpots?.type === 'fixed' && (
                    <div className="mt-2 border p-2 rounded-md bg-white space-y-1">
                      <label className="block text-sm font-medium text-[#172B4D] mb-2">
                        Jackpot Values (x Bet)
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        {(config.bonus?.jackpots?.levels || ['Minor', 'Major']).map((level) => (
                          <div key={level} className="flex items-center border p-1 bg-gray-50 rounded-md gap-2">
                            <label className="w-20 text-[#172B4D]">{level}:</label>
                            <input
                              type="number"
                              min="10"
                              max="100000"
                              value={level === 'Mini' ? 20 : level === 'Minor' ? 100 : level === 'Major' ? 1000 : 10000}
                              className="w-full bg-white border border-[#DFE1E6] rounded-lg px-3 py-1 text-[#172B4D]"
                            />
                            {/* <span className="text-[#5E6C84]">x</span> */}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Math Model Impact */}
      <div className="bg-white rounded-lg p-0  border border-[#DFE1E6] shadow-sm">
        <div
          className="w-full bg-gray-50 border-l-4 border-l-red-500 p-2 flex items-center justify-between text-left hover:bg-gray-50 transition-colors mb-"
        >
          <div className="flex flex-col items-start">
            <h3 className="text-lg font-semibold text-gray-900">Math Model Impact</h3>
            <p className="text-sm text-[#5E6C84] mt-1">Feature contribution to overall game math</p>

          </div>
          <button className="p-2 text-[#5E6C84] hover:text-[#172B4D] transition-colors">
          </button>
        </div>

        <div className="grid grid-cols-3 p-3 gap-6">
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-sm text-[#5E6C84]">Feature RTP</div>
            <div className="text-2xl font-bold text-[#172B4D]">{mathModel.featureRTP.toFixed(1)}%</div>
            <div className="text-xs text-[#5E6C84] mt-1">of total {(config as any).mathModel?.rtp || 96}% RTP</div>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-sm text-[#5E6C84]">Hit Frequency</div>
            <div className="text-2xl font-bold text-[#172B4D]">1:{Math.round(1 / mathModel.hitFrequency)}</div>
            <div className="text-xs text-[#5E6C84] mt-1">spins per feature</div>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="text-sm text-[#5E6C84]">Win Potential</div>
            <div className="text-2xl font-bold text-[#172B4D]">{mathModel.maxWin.toLocaleString()}x</div>
            <div className="text-xs text-[#5E6C84] mt-1">maximum feature win</div>
          </div>
        </div>

        {/* Feature Warnings */}
        {expandedFeatures.length > 3 && (
          <div className="mt-6 p-4 bg-[#FFFAE6] border border-[#FF991F] rounded-lg flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-[#FF8B00] mt-0.5" />
            <div>
              <h5 className="font-medium text-[#172B4D]">Feature Complexity Warning</h5>
              <p className="text-sm text-[#5E6C84] mt-1">
                Having more than 3 active bonus features may increase game complexity and affect player experience.
                Consider focusing on fewer, more impactful features.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Spacer div to maintain consistent spacing */}
      <div className="mt-10 bg-gray-50 h-2"></div>
    </div>
  );
};

export default BonusFeatures;